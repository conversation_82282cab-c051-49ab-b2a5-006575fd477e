import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const addToWishlistSchema = z.object({
  productId: z.string().min(1, 'Product ID is required')
})

// GET /api/wishlist - Get user's wishlist
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const wishlistItems = await prisma.wishlistItem.findMany({
      where: { userId: session.user.id },
      select: {
        id: true,
        userId: true,
        productId: true,
        createdAt: true,
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
            shortDescription: true,
            price: true,
            originalPrice: true,
            isOnSale: true,
            salePrice: true,
            images: true,
            categoryId: true,
            status: true,
            featured: true,
            downloadLimit: true,
            downloadExpiry: true,
            fileKey: true,
            fileName: true,
            fileSize: true,
            tags: true,
            metaTitle: true,
            metaDescription: true,
            createdAt: true,
            updatedAt: true,
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
                description: true,
                image: true,
                isActive: true,
                sortOrder: true,
                createdAt: true,
                updatedAt: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Transform the data to serialize properly
    const wishlistWithRatings = wishlistItems.map((item) => ({
      id: item.id,
      userId: item.userId,
      productId: item.productId,
      createdAt: item.createdAt,
      product: {
        id: item.product.id,
        name: item.product.name,
        slug: item.product.slug,
        description: item.product.description,
        shortDescription: item.product.shortDescription,
        price: Number(item.product.price),
        originalPrice: item.product.originalPrice ? Number(item.product.originalPrice) : null,
        isOnSale: item.product.isOnSale,
        salePrice: item.product.salePrice ? Number(item.product.salePrice) : null,
        images: item.product.images,
        categoryId: item.product.categoryId,
        status: item.product.status,
        featured: item.product.featured,
        downloadLimit: item.product.downloadLimit,
        downloadExpiry: item.product.downloadExpiry,
        fileKey: item.product.fileKey,
        fileName: item.product.fileName,
        fileSize: item.product.fileSize,
        tags: item.product.tags,
        metaTitle: item.product.metaTitle,
        metaDescription: item.product.metaDescription,
        createdAt: item.product.createdAt,
        updatedAt: item.product.updatedAt,
        averageRating: 0,
        category: item.product.category,
        reviewCount: 0,
        downloadCount: 0
      }
    }))

    // Convert BigInt values to strings for JSON serialization
    const serializedData = JSON.parse(JSON.stringify(wishlistWithRatings, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ))

    return NextResponse.json({
      success: true,
      data: serializedData
    })

  } catch (error) {
    console.error('Error fetching wishlist:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch wishlist'
    }, { status: 500 })
  }
}

// POST /api/wishlist - Add item to wishlist
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const { productId } = addToWishlistSchema.parse(body)

    // Verify product exists and is published
    const product = await prisma.product.findUnique({
      where: { 
        id: productId,
        status: 'PUBLISHED'
      }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'Product not found or not available'
      }, { status: 404 })
    }

    // Check if item already exists in wishlist
    const existingItem = await prisma.wishlistItem.findUnique({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId
        }
      }
    })

    if (existingItem) {
      return NextResponse.json({
        success: false,
        message: 'Product already in wishlist'
      }, { status: 400 })
    }

    // Add to wishlist
    const wishlistItem = await prisma.wishlistItem.create({
      data: {
        userId: session.user.id,
        productId
      },
      select: {
        id: true,
        userId: true,
        productId: true,
        createdAt: true,
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            description: true,
            shortDescription: true,
            price: true,
            originalPrice: true,
            isOnSale: true,
            salePrice: true,
            images: true,
            categoryId: true,
            status: true,
            featured: true,
            downloadLimit: true,
            downloadExpiry: true,
            fileKey: true,
            fileName: true,
            fileSize: true,
            tags: true,
            metaTitle: true,
            metaDescription: true,
            createdAt: true,
            updatedAt: true,
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
                description: true,
                image: true,
                isActive: true,
                sortOrder: true,
                createdAt: true,
                updatedAt: true
              }
            }
          }
        }
      }
    })

    // Transform the data to serialize properly
    const transformedItem = {
      id: wishlistItem.id,
      userId: wishlistItem.userId,
      productId: wishlistItem.productId,
      createdAt: wishlistItem.createdAt,
      product: {
        id: wishlistItem.product.id,
        name: wishlistItem.product.name,
        slug: wishlistItem.product.slug,
        description: wishlistItem.product.description,
        shortDescription: wishlistItem.product.shortDescription,
        price: Number(wishlistItem.product.price),
        originalPrice: wishlistItem.product.originalPrice ? Number(wishlistItem.product.originalPrice) : null,
        isOnSale: wishlistItem.product.isOnSale,
        salePrice: wishlistItem.product.salePrice ? Number(wishlistItem.product.salePrice) : null,
        images: wishlistItem.product.images,
        categoryId: wishlistItem.product.categoryId,
        status: wishlistItem.product.status,
        featured: wishlistItem.product.featured,
        downloadLimit: wishlistItem.product.downloadLimit,
        downloadExpiry: wishlistItem.product.downloadExpiry,
        fileKey: wishlistItem.product.fileKey,
        fileName: wishlistItem.product.fileName,
        fileSize: wishlistItem.product.fileSize,
        tags: wishlistItem.product.tags,
        metaTitle: wishlistItem.product.metaTitle,
        metaDescription: wishlistItem.product.metaDescription,
        createdAt: wishlistItem.product.createdAt,
        updatedAt: wishlistItem.product.updatedAt,
        category: wishlistItem.product.category
      }
    }

    // Convert BigInt values to strings for JSON serialization
    const serializedItem = JSON.parse(JSON.stringify(transformedItem, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ))

    return NextResponse.json({
      success: true,
      message: 'Added to wishlist',
      data: serializedItem
    })

  } catch (error) {
    console.error('Error adding to wishlist:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to add to wishlist'
    }, { status: 500 })
  }
}

// DELETE /api/wishlist - Remove item from wishlist
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')

    if (!productId) {
      return NextResponse.json({
        success: false,
        message: 'Product ID is required'
      }, { status: 400 })
    }

    // Remove from wishlist
    await prisma.wishlistItem.delete({
      where: {
        userId_productId: {
          userId: session.user.id,
          productId
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Removed from wishlist'
    })

  } catch (error) {
    console.error('Error removing from wishlist:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to remove from wishlist'
    }, { status: 500 })
  }
}
