import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAuth, requireAdmin } from '@/lib/auth-utils'
import { z } from 'zod'
import { ProductStatus } from '@prisma/client'

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  originalPrice: z.number().optional(),
  isOnSale: z.boolean().default(false),
  salePrice: z.number().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  status: z.nativeEnum(ProductStatus).default(ProductStatus.DRAFT),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  images: z.array(z.string()).default([]),
  fileKey: z.string().optional(),
  fileName: z.string().optional(),
  fileSize: z.number().optional(),
})

// GET /api/products - List products with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const featured = searchParams.get('featured') === 'true'
    const onSale = searchParams.get('onSale') === 'true'
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    const status = searchParams.get('status') as ProductStatus
    const includeAll = searchParams.get('includeAll') === 'true'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (includeAll) {
      // For admin queries, check if user is admin
      try {
        await requireAdmin()
        // Admin can see all products regardless of status
        if (status) {
          where.status = status
        }
      } catch {
        // Not admin, only show published
        where.status = ProductStatus.PUBLISHED
      }
    } else if (status) {
      where.status = status
    } else {
      // Only show published products for non-admin users
      where.status = ProductStatus.PUBLISHED
    }

    if (category) {
      where.category = {
        slug: category
      }
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { hasSome: [search] } }
      ]
    }

    if (featured) {
      where.featured = true
    }

    if (onSale) {
      where.isOnSale = true
    }

    if (minPrice || maxPrice) {
      where.price = {}
      if (minPrice) where.price.gte = parseFloat(minPrice)
      if (maxPrice) where.price.lte = parseFloat(maxPrice)
    }

    // Build orderBy clause
    const orderBy: any = {}
    if (sortBy === 'price') {
      orderBy.price = sortOrder
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder
    } else {
      orderBy.createdAt = sortOrder
    }

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: true,
          _count: {
            select: {
              reviews: true,
              downloads: true
            }
          }
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.product.count({ where })
    ])

    // Calculate average ratings and serialize BigInt
    const productsWithRatings = await Promise.all(
      products.map(async (product) => {
        const avgRating = await prisma.review.aggregate({
          where: { productId: product.id },
          _avg: { rating: true }
        })

        return {
          ...product,
          fileSize: product.fileSize ? product.fileSize.toString() : null,
          averageRating: avgRating._avg.rating || 0
        }
      })
    )

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: productsWithRatings,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error: any) {
    console.error('Error fetching products:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch products'
    }, { status: 500 })
  }
}

// POST /api/products - Create new product (Admin only)
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const validatedData = productSchema.parse(body)

    // Check if slug already exists
    const existingProduct = await prisma.product.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingProduct) {
      return NextResponse.json({
        success: false,
        message: 'Product slug already exists'
      }, { status: 400 })
    }

    // Verify category exists
    const category = await prisma.category.findUnique({
      where: { id: validatedData.categoryId }
    })

    if (!category) {
      return NextResponse.json({
        success: false,
        message: 'Category not found'
      }, { status: 400 })
    }

    // Prepare data for database (convert fileSize to BigInt if present)
    const productData = {
      ...validatedData,
      fileSize: validatedData.fileSize ? BigInt(validatedData.fileSize) : null
    }

    const product = await prisma.product.create({
      data: productData,
      include: {
        category: true
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedProduct = {
      ...product,
      fileSize: product.fileSize ? product.fileSize.toString() : null
    }

    return NextResponse.json({
      success: true,
      message: 'Product created successfully',
      data: serializedProduct
    }, { status: 201 })

  } catch (error: any) {
    console.error('Error creating product:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create product'
    }, { status: 500 })
  }
}
