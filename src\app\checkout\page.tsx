'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useCartStore } from '@/store/cart-store'
import { Button } from '@/components/ui/button'
import { StripePaymentForm } from '@/components/payments/stripe-payment-form'
import { ManualPaymentForm } from '@/components/payments/manual-payment-form'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import {
  CreditCardIcon,
  ShoppingBagIcon,
  ArrowLeftIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

export default function CheckoutPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { items, getTotalPrice, clearCart, isHydrated } = useCartStore()
  const [isProcessing, setIsProcessing] = useState(false)
  const [clientSecret, setClientSecret] = useState<string | null>(null)
  const [orderId, setOrderId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    email: session?.user?.email || '',
    firstName: session?.user?.firstName || '',
    lastName: session?.user?.lastName || '',
    paymentMethod: 'stripe',
    acceptTerms: false
  })
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null)
  const [couponError, setCouponError] = useState('')
  const [isValidatingCoupon, setIsValidatingCoupon] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'stripe' | 'manual'>('stripe')
  const [paymentMethods, setPaymentMethods] = useState<any[]>([])
  const [selectedManualMethod, setSelectedManualMethod] = useState<any>(null)

  const subtotalPrice = isHydrated ? getTotalPrice() : 0
  const couponDiscountAmount = appliedCoupon?.discountAmount || 0
  const manualPaymentDiscount = selectedPaymentMethod === 'manual' && selectedManualMethod
    ? (subtotalPrice * (selectedManualMethod.discountPercentage / 100))
    : 0
  const totalDiscountAmount = couponDiscountAmount + manualPaymentDiscount
  const totalPrice = Math.max(0, subtotalPrice - totalDiscountAmount)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/checkout')
    }
  }, [status, router])

  // Update form data when session loads
  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        email: session.user.email || '',
        firstName: session.user.firstName || '',
        lastName: session.user.lastName || ''
      }))
    }
  }, [session])

  // Fetch payment methods
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        const response = await fetch('/api/payment-methods')
        const data = await response.json()
        if (data.success) {
          setPaymentMethods(data.data)
          // Set default manual method to USDT if available
          const usdtMethod = data.data.find((method: any) => method.currency === 'USDT')
          if (usdtMethod) {
            setSelectedManualMethod(usdtMethod)
          }
        }
      } catch (error) {
        console.error('Error fetching payment methods:', error)
      }
    }

    fetchPaymentMethods()
  }, [])

  useEffect(() => {
    if (session?.user) {
      setFormData(prev => ({
        ...prev,
        email: session.user.email || '',
        firstName: session.user.name?.split(' ')[0] || '',
        lastName: session.user.name?.split(' ').slice(1).join(' ') || ''
      }))
    }
  }, [session])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponError('Please enter a coupon code')
      return
    }

    setIsValidatingCoupon(true)
    setCouponError('')

    try {
      const response = await fetch('/api/coupons/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: couponCode.trim(),
          subtotal: subtotalPrice,
          userId: session?.user?.id
        })
      })

      const data = await response.json()

      if (data.success) {
        setAppliedCoupon(data.data)
        toast.success(`Coupon applied! You saved ${formatCurrency(data.data.discountAmount)}`)
      } else {
        setCouponError(data.message)
        setAppliedCoupon(null)
      }
    } catch (error) {
      console.error('Error validating coupon:', error)
      setCouponError('Failed to validate coupon')
      setAppliedCoupon(null)
    } finally {
      setIsValidatingCoupon(false)
    }
  }

  const removeCoupon = () => {
    setCouponCode('')
    setAppliedCoupon(null)
    setCouponError('')
    toast.success('Coupon removed')
  }

  const createPaymentIntent = async () => {
    console.log('createPaymentIntent called')

    if (!formData.acceptTerms) {
      console.log('Terms not accepted')
      toast.error('Please accept the terms and conditions')
      return
    }

    if (items.length === 0) {
      console.log('Cart is empty')
      toast.error('Your cart is empty')
      return
    }

    console.log('Starting payment intent creation...')
    setIsProcessing(true)

    try {
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: items.map(item => ({
            productId: item.product.id,
            quantity: item.quantity
          })),
          customerInfo: {
            email: formData.email,
            firstName: formData.firstName,
            lastName: formData.lastName
          },
          couponCode: appliedCoupon?.coupon?.code,
          paymentMethod: selectedPaymentMethod,
          manualPaymentDiscount: selectedPaymentMethod === 'manual' && selectedManualMethod
            ? selectedManualMethod.discountPercentage
            : 0
        })
      })

      const data = await response.json()

      if (data.success) {
        if (selectedPaymentMethod === 'stripe') {
          setClientSecret(data.data.clientSecret)
        }
        setOrderId(data.data.orderId)
      } else {
        toast.error(data.message || 'Failed to create payment intent')
      }
    } catch (error) {
      console.error('Error creating payment intent:', error)
      toast.error('Failed to initialize payment')
    } finally {
      setIsProcessing(false)
    }
  }

  const handlePaymentSuccess = () => {
    clearCart()
    toast.success('Payment successful!')
    router.push(`/orders/success?orderId=${orderId}`)
  }

  const handlePaymentError = (error: string) => {
    toast.error(error)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted, creating payment intent...')
    console.log('Form data:', formData)
    console.log('Selected payment method:', selectedPaymentMethod)
    console.log('Items:', items)
    await createPaymentIntent()
  }

  if (status === 'loading' || !isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  if (isHydrated && items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ShoppingBagIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-4">Your cart is empty</h2>
          <p className="text-gray-400 mb-8">Add some products to your cart to continue</p>
          <Link href="/products">
            <Button variant="premium">
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/cart" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200 mb-4"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Cart
          </Link>
          <h1 className="text-3xl font-bold text-white">Checkout</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Checkout Form */}
          <div>
            {status === 'authenticated' && (
              <div className="space-y-6">
                {/* Contact Information */}
                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Contact Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      className="trading-input"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      className="trading-input"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="trading-input"
                  />
                </div>
              </div>

              {/* Payment Method Selection */}
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Payment Method</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Stripe Payment */}
                  <div
                    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                      selectedPaymentMethod === 'stripe'
                        ? 'border-yellow-400 bg-yellow-400/10'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                    onClick={() => setSelectedPaymentMethod('stripe')}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="paymentMethod"
                        value="stripe"
                        checked={selectedPaymentMethod === 'stripe'}
                        onChange={() => setSelectedPaymentMethod('stripe')}
                        className="w-4 h-4 text-yellow-400 bg-gray-700 border-gray-600 focus:ring-yellow-400"
                      />
                      <CreditCardIcon className="w-5 h-5 text-gray-400" />
                      <div>
                        <h4 className="font-medium text-white">Credit/Debit Card</h4>
                        <p className="text-sm text-gray-400">Pay with Visa, Mastercard, or American Express</p>
                      </div>
                    </div>
                  </div>

                  {/* Manual Payment */}
                  {paymentMethods.length > 0 && (
                    <div
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedPaymentMethod === 'manual'
                          ? 'border-yellow-400 bg-yellow-400/10'
                          : 'border-gray-600 hover:border-gray-500'
                      }`}
                      onClick={() => setSelectedPaymentMethod('manual')}
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="manual"
                          checked={selectedPaymentMethod === 'manual'}
                          onChange={() => setSelectedPaymentMethod('manual')}
                          className="w-4 h-4 text-yellow-400 bg-gray-700 border-gray-600 focus:ring-yellow-400"
                        />
                        <div className="w-5 h-5 bg-orange-500 rounded flex items-center justify-center">
                          <span className="text-white text-xs font-bold">₿</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-white">
                            {selectedManualMethod?.name || 'Cryptocurrency'}
                            {selectedManualMethod?.discountPercentage > 0 && (
                              <span className="ml-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                {selectedManualMethod.discountPercentage}% OFF
                              </span>
                            )}
                          </h4>
                          <p className="text-sm text-gray-400">
                            {selectedManualMethod?.currency || 'USDT'} payment with manual verification
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Manual Payment Method Selection */}
                {selectedPaymentMethod === 'manual' && paymentMethods.length > 1 && (
                  <div className="mt-4 space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      Select Cryptocurrency
                    </label>
                    <select
                      value={selectedManualMethod?.id || ''}
                      onChange={(e) => {
                        const method = paymentMethods.find(m => m.id === e.target.value)
                        setSelectedManualMethod(method)
                      }}
                      className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                    >
                      {paymentMethods.map((method) => (
                        <option key={method.id} value={method.id}>
                          {method.name} ({method.currency})
                          {method.discountPercentage > 0 && ` - ${method.discountPercentage}% OFF`}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>

              {/* Terms and Conditions */}
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  name="acceptTerms"
                  checked={formData.acceptTerms}
                  onChange={handleInputChange}
                  className="mt-1"
                  required
                />
                <label className="text-sm text-gray-300">
                  I agree to the{' '}
                  <Link href="/terms" className="text-yellow-400 hover:underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-yellow-400 hover:underline">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              {/* Payment Section */}
              {selectedPaymentMethod === 'stripe' ? (
                !clientSecret ? (
                  <form onSubmit={handleSubmit}>
                    <Button
                      type="submit"
                      disabled={isProcessing || !formData.acceptTerms}
                      variant="premium"
                      size="lg"
                      className="w-full"
                    >
                      {isProcessing ? (
                        <>
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                          Preparing Payment...
                        </>
                      ) : (
                        <>
                          <LockClosedIcon className="w-5 h-5 mr-2" />
                          Proceed to Payment - {formatCurrency(totalPrice)}
                        </>
                      )}
                    </Button>
                  </form>
                ) : (
                  <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                    <h2 className="text-xl font-semibold text-white mb-4">Complete Payment</h2>
                    <StripePaymentForm
                      clientSecret={clientSecret}
                      amount={totalPrice}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  </div>
                )
              ) : selectedPaymentMethod === 'manual' && selectedManualMethod ? (
                !orderId ? (
                  <form onSubmit={handleSubmit}>
                    <Button
                      type="submit"
                      disabled={isProcessing || !formData.acceptTerms}
                      variant="premium"
                      size="lg"
                      className="w-full"
                    >
                      {isProcessing ? (
                        <>
                          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                          Creating Order...
                        </>
                      ) : (
                        <>
                          <LockClosedIcon className="w-5 h-5 mr-2" />
                          Continue to Manual Payment - {formatCurrency(totalPrice)}
                        </>
                      )}
                    </Button>
                  </form>
                ) : (
                  <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                    <h2 className="text-xl font-semibold text-white mb-4">Manual Payment</h2>
                    <ManualPaymentForm
                      paymentMethod={selectedManualMethod}
                      orderId={orderId}
                      amount={totalPrice}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  </div>
                )
              ) : null}
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div>
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 sticky top-8">
              <h2 className="text-xl font-semibold text-white mb-6">Order Summary</h2>
              
              <div className="space-y-4 mb-6">
                {items.map((item) => {
                  const effectivePrice = item.product.isOnSale && item.product.salePrice
                    ? item.product.salePrice
                    : item.product.price

                  return (
                    <div key={item.id} className="flex items-center space-x-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-gray-700 rounded-lg overflow-hidden">
                        {item.product.images && item.product.images.length > 0 ? (
                          <Image
                            src={item.product.images[0]}
                            alt={item.product.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <ShoppingBagIcon className="w-6 h-6 text-gray-500" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-medium text-sm line-clamp-2">
                          {item.product.name}
                        </h3>
                        <p className="text-gray-400 text-xs">
                          Qty: {item.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-white font-semibold">
                          {formatCurrency(Number(effectivePrice) * item.quantity)}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* Coupon Section */}
              <div className="border-t border-gray-700 pt-4 mb-4">
                <h3 className="text-white font-medium mb-3">Coupon Code</h3>
                {!appliedCoupon ? (
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                        placeholder="Enter coupon code"
                        className="flex-1 trading-input text-sm"
                        onKeyPress={(e) => e.key === 'Enter' && validateCoupon()}
                      />
                      <Button
                        type="button"
                        onClick={validateCoupon}
                        disabled={isValidatingCoupon || !couponCode.trim()}
                        variant="outline"
                        size="sm"
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        {isValidatingCoupon ? (
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        ) : (
                          'Apply'
                        )}
                      </Button>
                    </div>
                    {couponError && (
                      <p className="text-red-400 text-xs">{couponError}</p>
                    )}
                  </div>
                ) : (
                  <div className="bg-green-900/20 border border-green-700/50 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-400 font-medium text-sm">
                          {appliedCoupon.coupon.code}
                        </p>
                        <p className="text-green-300 text-xs">
                          {appliedCoupon.coupon.name}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-green-400 font-medium text-sm">
                          -{formatCurrency(appliedCoupon.discountAmount)}
                        </span>
                        <button
                          type="button"
                          onClick={removeCoupon}
                          className="text-gray-400 hover:text-red-400 transition-colors"
                          title="Remove coupon"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Order Total */}
              <div className="border-t border-gray-700 pt-4">
                <div className="space-y-2 mb-3">
                  <div className="flex items-center justify-between text-gray-300">
                    <span>Subtotal</span>
                    <span>{formatCurrency(subtotalPrice)}</span>
                  </div>
                  {appliedCoupon && (
                    <div className="flex items-center justify-between text-green-400">
                      <span>Coupon Discount ({appliedCoupon.coupon.code})</span>
                      <span>-{formatCurrency(couponDiscountAmount)}</span>
                    </div>
                  )}
                  {manualPaymentDiscount > 0 && (
                    <div className="flex items-center justify-between text-orange-400">
                      <span>Payment Method Discount ({selectedManualMethod?.discountPercentage}%)</span>
                      <span>-{formatCurrency(manualPaymentDiscount)}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-between text-lg font-semibold text-white border-t border-gray-700 pt-3">
                  <span>Total</span>
                  <span className="text-yellow-400">{formatCurrency(totalPrice)}</span>
                </div>
                <p className="text-xs text-gray-400 mt-2">
                  Digital products - No shipping required
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
