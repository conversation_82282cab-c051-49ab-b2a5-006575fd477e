'use client'

import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'

interface TelegramPopupProps {
  show?: boolean
  delay?: number
  onClose?: () => void
}

export function TelegramPopup({ show = true, delay = 3000, onClose }: TelegramPopupProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (show) {
      const timer = setTimeout(() => {
        setIsVisible(true)
        setIsAnimating(true)
      }, delay)

      return () => clearTimeout(timer)
    }
  }, [show, delay])

  const handleClose = () => {
    setIsAnimating(false)
    setTimeout(() => {
      setIsVisible(false)
      onClose?.()
    }, 300)
  }

  const handleJoinTelegram = () => {
    window.open('https://t.me/forexbotzone', '_blank')
    handleClose()
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div 
        className={`
          bg-gradient-to-r from-blue-500 to-blue-600 
          text-white rounded-xl shadow-2xl border border-blue-400/20
          max-w-sm w-80 p-4
          transform transition-all duration-300 ease-out
          ${isAnimating ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-4 opacity-0 scale-95'}
        `}
      >
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-2 right-2 p-1 rounded-full hover:bg-white/20 transition-colors"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>

        {/* Content */}
        <div className="pr-6">
          {/* Telegram Icon */}
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 6.728-.896 6.728-.896 6.728-1.268 8.368-1.268 8.368-.159.708-.534.708-.534.708s-2.376-.366-3.611-.854c-.801-.318-1.503-.854-1.503-.854l-3.611-1.707s-.534-.318-.534-.708c0-.39.534-.708.534-.708l8.368-3.611s.708-.318.708-.708c0-.39-.708-.708-.708-.708l-8.368 3.611s-.534.318-.534.708c0 .39.534.708.534.708l3.611 1.707s.702.536 1.503.854c1.235.488 3.611.854 3.611.854s.375 0 .534-.708c0 0 .372-1.64 1.268-8.368 0 0 .727-4.87.896-6.728.169-1.858-.534-2.376-.534-2.376s-.708-.534-1.268-.534c-.56 0-1.268.534-1.268.534z"/>
              </svg>
            </div>
            <h3 className="font-bold text-lg">Join Our Community!</h3>
          </div>

          {/* Message */}
          <p className="text-blue-100 text-sm mb-4 leading-relaxed">
            🎉 <strong>Congratulations on your purchase!</strong><br/>
            Join our exclusive Telegram group for:
          </p>

          <ul className="text-blue-100 text-sm mb-4 space-y-1">
            <li>• 📈 Trading signals & tips</li>
            <li>• 🤝 Community support</li>
            <li>• 🎁 Exclusive bonuses</li>
            <li>• 📢 Product updates</li>
          </ul>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleJoinTelegram}
              className="flex-1 bg-white text-blue-600 hover:bg-blue-50 font-medium text-sm py-2"
            >
              Join Telegram
            </Button>
            <Button
              onClick={handleClose}
              variant="outline"
              className="px-3 border-white/30 text-white hover:bg-white/10 text-sm py-2"
            >
              Later
            </Button>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
        <div className="absolute top-1 -left-1 w-2 h-2 bg-white/40 rounded-full"></div>
      </div>
    </div>
  )
}
