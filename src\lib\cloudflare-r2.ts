import { S3<PERSON>lient, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

export interface UploadResult {
  key: string
  url: string
  size: number
}

export async function uploadFile(
  file: Buffer,
  key: string,
  contentType: string,
  metadata?: Record<string, string>
): Promise<UploadResult> {
  try {
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: file,
      ContentType: contentType,
      Metadata: metadata,
    })

    await r2Client.send(command)

    // For images, try to use public URL, for other files use signed URLs
    const isImage = contentType.startsWith('image/')
    let url: string

    if (isImage && PUBLIC_URL) {
      url = `${PUBLIC_URL}/${key}`
    } else {
      // Generate a signed URL for non-public files (max 7 days for S3 compatibility)
      url = await generateSignedUrl(key, 7 * 24 * 3600) // 7 days
    }

    return {
      key,
      url,
      size: file.length,
    }
  } catch (error) {
    console.error('Error uploading file to R2:', error)
    throw new Error('Failed to upload file')
  }
}

export async function deleteFile(key: string): Promise<void> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    })

    await r2Client.send(command)
  } catch (error) {
    console.error('Error deleting file from R2:', error)
    throw new Error('Failed to delete file')
  }
}

export async function generateSignedUrl(
  key: string,
  expiresIn: number = 3600,
  filename?: string
): Promise<string> {
  try {
    // Ensure expiration is within AWS S3 limits (max 7 days)
    const maxExpiration = 7 * 24 * 3600 // 7 days in seconds
    const validExpiresIn = Math.min(expiresIn, maxExpiration)

    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      ResponseContentDisposition: filename
        ? `attachment; filename="${filename}"`
        : undefined,
    })

    return await getSignedUrl(r2Client, command, { expiresIn: validExpiresIn })
  } catch (error) {
    console.error('Error generating signed URL:', error)
    throw new Error('Failed to generate signed URL')
  }
}

export async function generateSecureDownloadUrl(
  key: string,
  originalFilename: string,
  expiresIn: number = 3600
): Promise<string> {
  try {
    console.log('Generating secure download URL for:', {
      key,
      originalFilename,
      expiresIn,
      bucket: BUCKET_NAME
    })

    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      ResponseContentDisposition: `attachment; filename="${originalFilename}"`,
      ResponseCacheControl: 'no-cache, no-store, must-revalidate',
    })

    // Ensure expiration is within AWS S3 limits (max 7 days)
    const maxExpiration = 7 * 24 * 3600 // 7 days in seconds
    const validExpiresIn = Math.min(expiresIn, maxExpiration)

    const signedUrl = await getSignedUrl(r2Client, command, { expiresIn: validExpiresIn })
    console.log('Generated signed URL:', signedUrl)

    return signedUrl
  } catch (error) {
    console.error('Error generating secure download URL:', error)
    console.error('Error details:', {
      key,
      originalFilename,
      bucket: BUCKET_NAME,
      error: error instanceof Error ? error.message : String(error)
    })
    throw new Error('Failed to generate secure download URL')
  }
}

export function generateFileKey(
  type: 'product' | 'images' | 'image' | 'avatar',
  filename: string,
  userId?: string
): string {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)

  switch (type) {
    case 'product':
      return `products/${timestamp}-${randomString}-${filename}`
    case 'images':
    case 'image':
      return `images/${timestamp}-${randomString}-${filename}`
    case 'avatar':
      return `avatars/${userId || 'anonymous'}/${timestamp}-${randomString}-${filename}`
    default:
      return `misc/${timestamp}-${randomString}-${filename}`
  }
}

export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export function isValidFileType(filename: string, allowedTypes: string[]): boolean {
  const extension = getFileExtension(filename)
  return allowedTypes.includes(`.${extension}`)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Allowed file types for different categories
export const ALLOWED_FILE_TYPES = {
  PRODUCT_FILES: ['.ex4', '.ex5', '.mq4', '.mq5', '.zip', '.rar', '.pdf', '.txt'],
  IMAGES: ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
  DOCUMENTS: ['.pdf', '.doc', '.docx', '.txt'],
}

export const MAX_FILE_SIZES = {
  PRODUCT_FILE: 100 * 1024 * 1024, // 100MB
  IMAGE: 10 * 1024 * 1024, // 10MB
  DOCUMENT: 50 * 1024 * 1024, // 50MB
}
