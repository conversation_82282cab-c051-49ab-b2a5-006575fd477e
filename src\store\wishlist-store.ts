import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { WishlistItemWithProduct } from '@/types'
import toast from 'react-hot-toast'

interface WishlistStore {
  items: WishlistItemWithProduct[]
  isHydrated: boolean

  // Actions
  addItem: (productId: string) => Promise<void>
  removeItem: (productId: string) => Promise<void>
  toggleItem: (productId: string) => Promise<void>
  fetchWishlist: () => Promise<void>
  clearWishlist: () => void
  setHydrated: () => void

  // Computed values
  hasItem: (productId: string) => boolean
  getItemCount: () => number
}

export const useWishlistStore = create<WishlistStore>()(
  persist(
    (set, get) => ({
      items: [],
      isHydrated: false,

      setHydrated: () => {
        set({ isHydrated: true })
      },

      addItem: async (productId: string) => {
        try {
          // Check if already in wishlist
          if (get().hasItem(productId)) {
            toast.error('Product already in wishlist')
            return
          }

          const response = await fetch('/api/wishlist', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ productId }),
          })

          const data = await response.json()

          if (data.success) {
            // Fetch updated wishlist to ensure sync
            await get().fetchWishlist()
            toast.success('Added to wishlist!')
          } else {
            toast.error(data.message || 'Failed to add to wishlist')
          }
        } catch (error) {
          console.error('Error adding to wishlist:', error)
          toast.error('Failed to add to wishlist')
        }
      },

      removeItem: async (productId: string) => {
        try {
          const response = await fetch(`/api/wishlist?productId=${productId}`, {
            method: 'DELETE',
          })

          const data = await response.json()

          if (data.success) {
            // Remove from local state
            set({
              items: get().items.filter(item => item.product.id !== productId)
            })
            toast.success('Removed from wishlist!')
          } else {
            toast.error(data.message || 'Failed to remove from wishlist')
          }
        } catch (error) {
          console.error('Error removing from wishlist:', error)
          toast.error('Failed to remove from wishlist')
        }
      },

      toggleItem: async (productId: string) => {
        const hasItem = get().hasItem(productId)
        if (hasItem) {
          await get().removeItem(productId)
        } else {
          await get().addItem(productId)
        }
      },

      fetchWishlist: async () => {
        try {
          console.log('Fetching wishlist...')
          const response = await fetch('/api/wishlist')
          const data = await response.json()
          console.log('Wishlist API response:', data)

          if (data.success) {
            const items = data.data || []
            console.log('Setting wishlist items:', items.length)
            set({ items })
          } else {
            // If unauthorized, clear the wishlist
            if (response.status === 401) {
              console.log('Unauthorized, clearing wishlist')
              set({ items: [] })
            } else {
              console.error('Failed to fetch wishlist:', data.message)
            }
          }
        } catch (error) {
          console.error('Error fetching wishlist:', error)
          set({ items: [] })
        }
      },

      clearWishlist: () => {
        set({ items: [] })
      },

      hasItem: (productId: string) => {
        const items = get().items
        const hasItem = items.some(item => item.product.id === productId)
        console.log('Checking wishlist for product:', productId, 'Has item:', hasItem, 'Total items:', items.length)
        return hasItem
      },

      getItemCount: () => {
        return get().items.length
      },
    }),
    {
      name: 'wishlist-storage',
      partialize: (state) => ({ items: state.items }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.setHydrated()
        }
      },
    }
  )
)
