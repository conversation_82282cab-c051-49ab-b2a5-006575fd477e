'use client'

import { useState, useEffect } from 'react'
import { ProductCard } from './product-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Button } from '@/components/ui/button'
import { ProductFilters, ProductWithRelations } from '@/types'

interface ProductsGridProps {
  filters: ProductFilters
  currentPage: number
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export function ProductsGrid({ filters, currentPage }: ProductsGridProps) {
  const [products, setProducts] = useState<ProductWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)

  const PRODUCTS_PER_PAGE = 12

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true)
      setError(null)

      try {
        // Build query parameters
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: PRODUCTS_PER_PAGE.toString(),
        })

        if (filters.category) params.append('category', filters.category)
        if (filters.search) params.append('search', filters.search)
        if (filters.minPrice !== undefined) params.append('minPrice', filters.minPrice.toString())
        if (filters.maxPrice !== undefined) params.append('maxPrice', filters.maxPrice.toString())
        if (filters.featured !== undefined) params.append('featured', filters.featured.toString())
        if (filters.onSale !== undefined) params.append('onSale', filters.onSale.toString())
        if (filters.sortBy) params.append('sortBy', filters.sortBy)
        if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)
        if (filters.tags && filters.tags.length > 0) params.append('tags', filters.tags.join(','))

        const response = await fetch(`/api/products?${params.toString()}`)
        const data = await response.json()

        if (response.ok && data.success) {
          setProducts(data.data || [])
          setPagination(data.pagination)
        } else {
          setError(data.message || 'Failed to fetch products')
        }
      } catch (error) {
        console.error('Error fetching products:', error)
        setError('Failed to fetch products')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [filters, currentPage])

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-red-900/20 flex items-center justify-center">
          <svg className="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">Error loading products</h3>
        <p className="text-gray-400 mb-6">{error}</p>
        <Button
          onClick={() => window.location.reload()}
          className="bg-yellow-400 hover:bg-yellow-500 text-black"
        >
          Try Again
        </Button>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-gray-400 text-2xl">🔍</span>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">No products found</h3>
        <p className="text-gray-400 mb-6">
          Try adjusting your filters or search terms to find what you're looking for.
        </p>
        <Button variant="premium" onClick={() => window.location.reload()}>
          Clear Filters
        </Button>
      </div>
    )
  }

  return (
    <div>
      {/* Results Header */}
      <div className="flex items-center justify-between mb-6">
        <p className="text-gray-300">
          {pagination ? (
            <>Showing {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} products</>
          ) : (
            <>Showing {products.length} products</>
          )}
        </p>
        <div className="flex items-center space-x-2">
          <span className="text-gray-400 text-sm">Sort by:</span>
          <select className="bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-1 text-sm focus:border-yellow-500 focus:ring-yellow-500">
            <option value="createdAt">Newest</option>
            <option value="price">Price: Low to High</option>
            <option value="price-desc">Price: High to Low</option>
            <option value="name">Name</option>
            <option value="rating">Rating</option>
          </select>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <Button
            variant="ghost"
            disabled={!pagination?.hasPrev}
            onClick={() => {
              const url = new URL(window.location.href)
              url.searchParams.set('page', String(currentPage - 1))
              window.location.href = url.toString()
            }}
          >
            Previous
          </Button>

          {pagination && Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={page === currentPage ? "premium" : "ghost"}
              size="sm"
              onClick={() => {
                const url = new URL(window.location.href)
                url.searchParams.set('page', String(page))
                window.location.href = url.toString()
              }}
            >
              {page}
            </Button>
          ))}

          <Button
            variant="ghost"
            disabled={!pagination?.hasNext}
            onClick={() => {
              const url = new URL(window.location.href)
              url.searchParams.set('page', String(currentPage + 1))
              window.location.href = url.toString()
            }}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
