import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { uploadFile, generateFileKey } from '@/lib/cloudflare-r2'

// POST /api/upload/payment-proof - Upload payment proof image
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file provided'
      }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      }, { status: 400 })
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        message: 'File size too large. Maximum size is 5MB.'
      }, { status: 400 })
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 8)
    const extension = file.name.split('.').pop()
    const userId = session?.user?.id || 'guest'
    const fileName = `payment-proof-${userId}-${timestamp}-${randomString}.${extension}`

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Upload to Cloudflare R2
    const fileKey = generateFileKey('image', fileName)
    const uploadResult = await uploadFile(buffer, fileKey, file.type)

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        url: uploadResult.url,
        key: uploadResult.key,
        fileName: fileName,
        originalName: file.name,
        size: uploadResult.size,
        type: file.type
      }
    })

  } catch (error) {
    console.error('Error uploading payment proof:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to upload file'
    }, { status: 500 })
  }
}
