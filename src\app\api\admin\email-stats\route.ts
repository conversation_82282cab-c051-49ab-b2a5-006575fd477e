import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { EmailStatus, EmailTemplateType } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    // Get overall statistics
    const [
      totalSent,
      totalDelivered,
      totalOpened,
      totalClicked,
      totalBounced,
      totalFailed,
      welcomeEmails,
      orderConfirmations,
      passwordResets,
      supportConfirmations,
      supportReplies
    ] = await Promise.all([
      prisma.emailLog.count(),
      prisma.emailLog.count({ where: { status: EmailStatus.DELIVERED } }),
      prisma.emailLog.count({ where: { status: EmailStatus.OPENED } }),
      prisma.emailLog.count({ where: { status: EmailStatus.CLICKED } }),
      prisma.emailLog.count({ where: { status: EmailStatus.BOUNCED } }),
      prisma.emailLog.count({ where: { status: EmailStatus.FAILED } }),
      prisma.emailLog.count({
        where: {
          template: { type: EmailTemplateType.WELCOME }
        }
      }),
      prisma.emailLog.count({
        where: {
          template: { type: EmailTemplateType.ORDER_CONFIRMATION }
        }
      }),
      prisma.emailLog.count({
        where: {
          template: { type: EmailTemplateType.PASSWORD_RESET }
        }
      }),
      prisma.emailLog.count({
        where: {
          template: { type: EmailTemplateType.SUPPORT_CONFIRMATION }
        }
      }),
      prisma.emailLog.count({
        where: {
          template: { type: EmailTemplateType.SUPPORT_REPLY }
        }
      })
    ])

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentActivity = await prisma.emailLog.groupBy({
      by: ['status'],
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      _count: {
        id: true
      }
    })

    // Get daily stats for the last 7 days (simplified for now)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const recentEmails = await prisma.emailLog.findMany({
      where: {
        createdAt: {
          gte: sevenDaysAgo
        }
      },
      select: {
        status: true,
        createdAt: true
      }
    })

    // Group by date manually
    const dailyStats = recentEmails.reduce((acc: any[], email) => {
      const date = email.createdAt.toISOString().split('T')[0]
      const existing = acc.find(item => item.date === date)

      if (existing) {
        existing.total++
        if (email.status === 'DELIVERED') existing.delivered++
        if (email.status === 'FAILED') existing.failed++
      } else {
        acc.push({
          date,
          total: 1,
          delivered: email.status === 'DELIVERED' ? 1 : 0,
          failed: email.status === 'FAILED' ? 1 : 0
        })
      }

      return acc
    }, [])

    const stats = {
      overview: {
        totalSent,
        totalDelivered,
        totalOpened,
        totalClicked,
        totalBounced,
        totalFailed,
        deliveryRate: totalSent > 0 ? ((totalDelivered / totalSent) * 100).toFixed(2) : '0',
        openRate: totalDelivered > 0 ? ((totalOpened / totalDelivered) * 100).toFixed(2) : '0',
        clickRate: totalOpened > 0 ? ((totalClicked / totalOpened) * 100).toFixed(2) : '0',
        bounceRate: totalSent > 0 ? ((totalBounced / totalSent) * 100).toFixed(2) : '0'
      },
      byType: {
        welcomeEmails,
        orderConfirmations,
        passwordResets,
        supportConfirmations,
        supportReplies
      },
      recentActivity: recentActivity.reduce((acc, item) => {
        acc[item.status.toLowerCase()] = item._count.id
        return acc
      }, {} as Record<string, number>),
      dailyStats
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error: any) {
    console.error('Error fetching email statistics:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch email statistics'
    }, { status: 500 })
  }
}
