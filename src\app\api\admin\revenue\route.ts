import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Get revenue data
    const [
      totalRevenue,
      monthlyRevenue,
      dailyRevenue,
      recentTransactions,
      paymentMethodStats
    ] = await Promise.all([
      prisma.order.aggregate({
        where: {
          status: 'COMPLETED'
        },
        _sum: {
          totalAmount: true
        }
      }),

      prisma.order.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: startDate
          }
        },
        _sum: {
          totalAmount: true
        }
      }),

      prisma.order.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        _sum: {
          totalAmount: true
        }
      }),

      prisma.order.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        select: {
          id: true,
          totalAmount: true,
          createdAt: true,
          status: true,
          paymentMethod: true
        }
      }),

      prisma.order.groupBy({
        by: ['paymentMethod'],
        where: {
          status: 'COMPLETED',
          paymentMethod: {
            not: null
          }
        },
        _sum: {
          totalAmount: true
        },
        _count: {
          id: true
        }
      })
    ])

    // Process payment methods data
    const totalRevenueAmount = Number(totalRevenue._sum.totalAmount || 0)
    const paymentMethods = paymentMethodStats.map(stat => {
      const amount = Number(stat._sum.totalAmount || 0)
      const percentage = totalRevenueAmount > 0 ? (amount / totalRevenueAmount) * 100 : 0

      // Map payment method names to display names
      let displayName = stat.paymentMethod || 'Unknown'
      if (stat.paymentMethod === 'stripe') {
        displayName = 'Stripe'
      } else if (stat.paymentMethod === 'manual') {
        displayName = 'Manual Payment'
      }

      return {
        method: displayName,
        amount: amount,
        percentage: Math.round(percentage * 10) / 10 // Round to 1 decimal place
      }
    }).sort((a, b) => b.amount - a.amount) // Sort by amount descending

    // Calculate growth rate (simplified calculation)
    const previousPeriodStart = new Date(startDate)
    const periodLength = now.getTime() - startDate.getTime()
    previousPeriodStart.setTime(startDate.getTime() - periodLength)

    const previousPeriodRevenue = await prisma.order.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate
        }
      },
      _sum: {
        totalAmount: true
      }
    })

    const currentPeriodAmount = Number(monthlyRevenue._sum.totalAmount || 0)
    const previousPeriodAmount = Number(previousPeriodRevenue._sum.totalAmount || 0)
    const revenueGrowth = previousPeriodAmount > 0
      ? ((currentPeriodAmount - previousPeriodAmount) / previousPeriodAmount) * 100
      : currentPeriodAmount > 0 ? 100 : 0

    const revenue = {
      totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
      monthlyRevenue: Number(monthlyRevenue._sum.totalAmount || 0),
      dailyRevenue: Number(dailyRevenue._sum.totalAmount || 0),
      revenueGrowth,
      paymentMethods,
      recentTransactions: recentTransactions.map(transaction => {
        // Map payment method to display name
        let displayMethod = transaction.paymentMethod || 'Unknown'
        if (transaction.paymentMethod === 'stripe') {
          displayMethod = 'Stripe'
        } else if (transaction.paymentMethod === 'manual') {
          displayMethod = 'Manual Payment'
        }

        return {
          id: transaction.id.slice(-8),
          amount: Number(transaction.totalAmount),
          method: displayMethod,
          date: transaction.createdAt.toISOString().split('T')[0],
          status: transaction.status.toLowerCase()
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: revenue
    })

  } catch (error: any) {
    console.error('Error fetching revenue:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch revenue'
    }, { status: 500 })
  }
}
