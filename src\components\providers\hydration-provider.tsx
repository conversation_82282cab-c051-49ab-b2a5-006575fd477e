'use client'

import { useEffect } from 'react'
import { useCartStore } from '@/store/cart-store'

interface HydrationProviderProps {
  children: React.ReactNode
}

export function HydrationProvider({ children }: HydrationProviderProps) {
  const setHydrated = useCartStore((state) => state.setHydrated)

  useEffect(() => {
    // Mark the store as hydrated after the component mounts
    setHydrated()
  }, [setHydrated])

  return <>{children}</>
}
