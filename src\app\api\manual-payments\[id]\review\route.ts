import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'
import { sendOrderConfirmationEmail } from '@/lib/email'
import { z } from 'zod'

const reviewPaymentSchema = z.object({
  status: z.enum(['APPROVED', 'REJECTED']),
  adminNotes: z.string().optional()
})

// PUT /api/manual-payments/[id]/review - Review manual payment (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const admin = await requireAdmin()
    const body = await request.json()
    const { status, adminNotes } = reviewPaymentSchema.parse(body)

    // Get the manual payment with order details
    const manualPayment = await prisma.manualPayment.findUnique({
      where: { id: params.id },
      include: {
        order: {
          include: {
            user: true,
            items: {
              include: {
                product: {
                  include: {
                    downloadFiles: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!manualPayment) {
      return NextResponse.json({
        success: false,
        message: 'Manual payment not found'
      }, { status: 404 })
    }

    if (manualPayment.status !== 'PENDING') {
      return NextResponse.json({
        success: false,
        message: 'Manual payment has already been reviewed'
      }, { status: 400 })
    }

    // Update manual payment status
    const updatedPayment = await prisma.manualPayment.update({
      where: { id: params.id },
      data: {
        status,
        reviewedAt: new Date(),
        reviewedBy: admin.id,
        adminNotes
      }
    })

    if (status === 'APPROVED') {
      console.log(`Approving manual payment ${params.id} for order ${manualPayment.orderId}`)

      // Update order status to completed
      await prisma.order.update({
        where: { id: manualPayment.orderId },
        data: {
          status: 'COMPLETED',
          paymentStatus: 'COMPLETED',
          adminNotes
        }
      })

      // Create purchase records for each product
      for (const item of manualPayment.order.items) {
        console.log(`Creating purchase record for user ${manualPayment.order.userId}, product ${item.productId}`)

        // Create purchase record
        await prisma.purchase.create({
          data: {
            userId: manualPayment.order.userId!,
            productId: item.productId,
            orderId: manualPayment.orderId,
            price: item.price,
            status: 'COMPLETED'
          }
        })
      }

      // Clear user's cart
      if (manualPayment.order.userId) {
        try {
          await prisma.cartItem.deleteMany({
            where: { userId: manualPayment.order.userId }
          })
          console.log(`Cleared cart for user ${manualPayment.order.userId}`)
        } catch (cartError) {
          console.error('Failed to clear user cart:', cartError)
          // Don't fail the approval if cart clearing fails
        }
      }

      // Send order confirmation email
      try {
        await sendOrderConfirmationEmail(manualPayment.order.email, {
          orderNumber: manualPayment.order.orderNumber,
          customerName: `${manualPayment.order.firstName} ${manualPayment.order.lastName}`,
          items: manualPayment.order.items.map(item => ({
            name: item.product.name,
            quantity: item.quantity,
            price: Number(item.price)
          })),
          total: Number(manualPayment.order.totalAmount),
          downloadUrl: `${process.env.NEXTAUTH_URL || process.env.SITE_URL}/downloads`
        })
      } catch (emailError) {
        console.error('Failed to send order confirmation email:', emailError)
        // Don't fail the approval if email fails
      }
    } else if (status === 'REJECTED') {
      console.log(`Rejecting manual payment ${params.id} for order ${manualPayment.orderId}`)

      // Update order status to cancelled
      await prisma.order.update({
        where: { id: manualPayment.orderId },
        data: {
          status: 'CANCELLED',
          paymentStatus: 'FAILED',
          adminNotes
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: `Manual payment ${status.toLowerCase()} successfully`,
      data: {
        ...updatedPayment,
        amount: Number(updatedPayment.amount)
      }
    })

  } catch (error) {
    console.error('Error reviewing manual payment:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to review manual payment'
    }, { status: 500 })
  }
}
