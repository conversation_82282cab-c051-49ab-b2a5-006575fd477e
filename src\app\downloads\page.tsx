'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import {
  ArrowDownTrayIcon,
  ClockIcon,
  DocumentArrowDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'

interface Download {
  id: string
  productId: string
  product: {
    id: string
    name: string
    slug: string
    images: string[]
    fileKey?: string
    fileName?: string
  }
  purchaseDate: string
  hasToken: boolean
  token: string | null
  downloadCount: number
  maxDownloads: number
  expiresAt: string | null
  isActive: boolean
  createdAt: string
}

export default function DownloadsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [downloads, setDownloads] = useState<Download[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (status === 'authenticated') {
      fetchDownloads()
    }
  }, [status, router])

  const fetchDownloads = async () => {
    try {
      const response = await fetch('/api/user/downloads')
      const data = await response.json()

      if (data.success) {
        setDownloads(data.data)
      } else {
        toast.error('Failed to load downloads')
      }
    } catch (error) {
      console.error('Error fetching downloads:', error)
      toast.error('Failed to load downloads')
    } finally {
      setLoading(false)
    }
  }



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">My Downloads</h1>
          <p className="text-gray-300">Access your purchased products and download files</p>
        </div>

        {downloads.length === 0 ? (
          <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-8 text-center">
            <div className="w-16 h-16 text-gray-400 mx-auto mb-4">📄</div>
            <h3 className="text-xl font-semibold text-white mb-2">No Downloads Available</h3>
            <p className="text-gray-400 mb-6">
              You haven't purchased any products yet. Browse our collection to get started.
            </p>
            <button
              onClick={() => router.push('/products')}
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium px-4 py-2 rounded"
            >
              Browse Products
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {downloads.map((download) => (
              <DownloadCard
                key={download.id}
                download={download}
                onDownloadUpdate={fetchDownloads}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

interface DownloadCardProps {
  download: Download
  onDownloadUpdate: () => void
}

function DownloadCard({ download, onDownloadUpdate }: DownloadCardProps) {
  const [downloading, setDownloading] = useState(false)
  const [creatingToken, setCreatingToken] = useState(false)

  const createDownloadToken = async () => {
    try {
      setCreatingToken(true)
      const response = await fetch('/api/downloads/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: download.productId,
          maxDownloads: 5,
          expiryDays: 30
        }),
      })

      const data = await response.json()

      if (data.success) {
        onDownloadUpdate()
        toast.success('Download access granted!')
      } else {
        toast.error(data.message || 'Failed to create download access')
      }
    } catch (error) {
      console.error('Error creating download token:', error)
      toast.error('Failed to create download access')
    } finally {
      setCreatingToken(false)
    }
  }

  const handleDownload = async () => {
    if (!download.token) {
      toast.error('No download token available')
      return
    }

    try {
      setDownloading(true)

      // Create a temporary link and click it to trigger download
      const downloadUrl = `/api/downloads/by-token/${download.token}`
      const link = document.createElement('a')
      link.href = downloadUrl
      link.target = '_blank'
      link.download = download.product.fileName || `${download.product.name}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Update download status after a short delay
      setTimeout(() => {
        onDownloadUpdate()
      }, 1000)

      toast.success('Download started!')
    } catch (error) {
      console.error('Error downloading file:', error)
      toast.error('Failed to download file')
    } finally {
      setDownloading(false)
    }
  }

  // Downloads are now unlimited and never expire

  return (
    <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
      <div className="flex items-start gap-4">
        {/* Product Image */}
        <div className="flex-shrink-0">
          {download.product.images && download.product.images.length > 0 ? (
            <Image
              src={download.product.images[0]}
              alt={download.product.name}
              width={80}
              height={80}
              className="rounded-lg object-cover"
            />
          ) : (
            <div className="w-20 h-20 bg-gray-700 rounded-lg flex items-center justify-center">
              <DocumentArrowDownIcon className="w-8 h-8 text-gray-400" />
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-white mb-1 truncate">
            {download.product.name}
          </h3>
          <p className="text-gray-400 text-sm mb-3">
            Purchase Date: {new Date(download.purchaseDate).toLocaleDateString()}
          </p>

          {/* Download Status */}
          <div className="space-y-2">
            {download.hasToken ? (
              <>
                <div className="flex items-center gap-2 text-green-400 text-sm">
                  <CheckCircleIcon className="w-4 h-4" />
                  <span>Lifetime access - unlimited downloads</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <span>Total downloads: {download.downloadCount || 0}</span>
                </div>
              </>
            ) : (
              <p className="text-gray-400 text-sm">No download access created yet</p>
            )}
          </div>
        </div>

        {/* Download Actions */}
        <div className="flex-shrink-0">
          {download.hasToken ? (
            <Button
              onClick={handleDownload}
              disabled={downloading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {downloading ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                  Downloading...
                </>
              ) : (
                <>
                  <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                  Download Now
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={createDownloadToken}
              disabled={creatingToken}
              variant="outline"
              className="border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black"
            >
              {creatingToken ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                'Create Download Access'
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
