import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { constructWebhookEvent } from '@/lib/stripe'
import { sendOrderConfirmationEmail } from '@/lib/email'
import { prisma } from '@/lib/prisma'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = headers()
    const signature = headersList.get('stripe-signature')

    console.log('Webhook received:', {
      hasSignature: !!signature,
      bodyLength: body.length,
      headers: Object.fromEntries(headersList.entries())
    })

    if (!signature) {
      console.error('Missing stripe-signature header')
      return NextResponse.json({
        success: false,
        message: 'Missing stripe-signature header'
      }, { status: 400 })
    }

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
    if (!webhookSecret) {
      console.error('STRIPE_WEBHOOK_SECRET not configured')
      return NextResponse.json({
        success: false,
        message: 'Webhook secret not configured'
      }, { status: 500 })
    }

    // Construct the event
    const event = constructWebhookEvent(body, signature, webhookSecret)
    console.log('Webhook event constructed:', event.type, event.id)

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent)
        break

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ success: true, received: true })

  } catch (error: any) {
    console.error('Webhook error:', {
      message: error.message,
      stack: error.stack,
      type: error.constructor.name
    })

    // Return 200 for webhook signature errors to prevent retries
    const status = error.message?.includes('signature') ? 200 : 400

    return NextResponse.json({
      success: false,
      message: error.message || 'Webhook processing failed'
    }, { status })
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, stripe-signature',
    },
  })
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    const orderId = paymentIntent.metadata.orderId
    
    if (!orderId) {
      console.error('No orderId in payment intent metadata')
      return
    }

    // Update order status
    const order = await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'COMPLETED',
        paymentStatus: 'COMPLETED'
      },
      include: {
        user: true,
        items: {
          include: {
            product: {
              include: {
                downloadFiles: true
              }
            }
          }
        }
      }
    })

    // Create purchase records for each product
    for (const item of order.items) {
      await prisma.purchase.create({
        data: {
          userId: order.userId,
          productId: item.productId,
          orderId: order.id,
          price: item.price,
          status: 'COMPLETED'
        }
      })
    }

    // Clear user's cart (only for authenticated users)
    if (order.userId) {
      await prisma.cartItem.deleteMany({
        where: { userId: order.userId }
      })
    }

    // Send order confirmation email
    try {
      await sendOrderConfirmationEmail(order.email, {
        orderNumber: order.orderNumber,
        customerName: `${order.firstName} ${order.lastName}`.trim(),
        items: order.items.map(item => ({
          name: item.product.name,
          quantity: item.quantity,
          price: Number(item.price)
        })),
        total: Number(order.totalAmount),
        downloadUrl: `${process.env.NEXTAUTH_URL || process.env.SITE_URL}/downloads`
      })
      console.log(`Order confirmation email sent to: ${order.email}`)
    } catch (emailError) {
      console.error('Failed to send order confirmation email:', emailError)
      // Don't fail the webhook if email fails
    }

    console.log(`Order ${order.orderNumber} completed successfully`)

    await handleSuccessfulPayment(paymentIntent)

  } catch (error) {
    console.error('Error handling payment intent succeeded:', error)
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    const orderId = paymentIntent.metadata.orderId
    
    if (!orderId) {
      console.error('No orderId in payment intent metadata')
      return
    }

    // Update order status
    await prisma.order.update({
      where: { id: orderId },
      data: {
        status: 'CANCELLED',
        paymentStatus: 'FAILED'
      }
    })

    // TODO: Send failure notification email
    console.log(`Payment failed for order ${orderId}`)

    await handleFailedPayment(paymentIntent)

  } catch (error) {
    console.error('Error handling payment intent failed:', error)
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    // Handle subscription payment success
    const subscriptionId = invoice.subscription as string
    const customerId = invoice.customer as string

    // TODO: Update membership status
    console.log(`Invoice payment succeeded for subscription ${subscriptionId}`)

  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error)
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  try {
    const customerId = subscription.customer as string
    
    // TODO: Create or update membership record
    console.log(`Subscription created: ${subscription.id}`)

  } catch (error) {
    console.error('Error handling subscription created:', error)
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    // TODO: Update membership status based on subscription status
    console.log(`Subscription updated: ${subscription.id}`)

  } catch (error) {
    console.error('Error handling subscription updated:', error)
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    // TODO: Cancel membership
    console.log(`Subscription deleted: ${subscription.id}`)

  } catch (error) {
    console.error('Error handling subscription deleted:', error)
  }
}
