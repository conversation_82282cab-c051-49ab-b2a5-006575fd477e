'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { ArrowDownTrayIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface DownloadButtonProps {
  productId: string
  className?: string
}

interface DownloadStatus {
  hasAccess: boolean
  token?: string
  downloadCount: number
  maxDownloads: number
  remainingDownloads: number
  expiresAt?: string
  isExpired: boolean
}

export function DownloadButton({ productId, className = '' }: DownloadButtonProps) {
  const { data: session, status } = useSession()
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [downloading, setDownloading] = useState(false)

  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      fetchDownloadStatus()
    }
  }, [status, session, productId])

  const fetchDownloadStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/downloads/token?productId=${productId}`)
      const data = await response.json()

      if (data.success) {
        setDownloadStatus(data.data)
      } else {
        console.error('Failed to fetch download status:', data.message)
      }
    } catch (error) {
      console.error('Error fetching download status:', error)
    } finally {
      setLoading(false)
    }
  }

  const createDownloadToken = async () => {
    try {
      const response = await fetch('/api/downloads/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          maxDownloads: 5,
          expiryDays: 30
        }),
      })

      const data = await response.json()

      if (data.success) {
        await fetchDownloadStatus()
        toast.success('Download access granted!')
      } else {
        toast.error(data.message || 'Failed to create download access')
      }
    } catch (error) {
      console.error('Error creating download token:', error)
      toast.error('Failed to create download access')
    }
  }

  const handleDownload = async () => {
    if (!downloadStatus?.token) {
      toast.error('No download token available')
      return
    }

    try {
      setDownloading(true)
      const response = await fetch(`/api/downloads/by-token/${downloadStatus.token}`)
      const data = await response.json()

      if (data.success) {
        // Open download URL in new tab
        window.open(data.data.downloadUrl, '_blank')

        // Update download status
        await fetchDownloadStatus()

        toast.success(`Download started! ${data.data.remainingDownloads} downloads remaining`)
      } else {
        // Show more user-friendly error messages
        if (data.message?.includes('not found in storage') || data.message?.includes('not available')) {
          toast.error('Download file is currently unavailable. Please contact support for assistance.')
        } else {
          toast.error(data.message || 'Failed to generate download link')
        }
      }
    } catch (error) {
      console.error('Error downloading file:', error)
      toast.error('Failed to download file')
    } finally {
      setDownloading(false)
    }
  }

  // Not authenticated
  if (status !== 'authenticated') {
    return (
      <Button 
        disabled 
        className={`${className} opacity-50 cursor-not-allowed`}
      >
        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
        Login to Download
      </Button>
    )
  }

  // Loading
  if (loading) {
    return (
      <Button 
        disabled 
        className={className}
      >
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
        Checking Access...
      </Button>
    )
  }

  // No access (not purchased)
  if (downloadStatus && !downloadStatus.hasAccess) {
    return (
      <Button 
        disabled 
        className={`${className} opacity-50 cursor-not-allowed`}
      >
        <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
        Purchase Required
      </Button>
    )
  }

  // Has access but no token yet
  if (downloadStatus?.hasAccess && !downloadStatus.token) {
    return (
      <Button 
        onClick={createDownloadToken}
        className={className}
      >
        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
        Activate Download
      </Button>
    )
  }

  // Downloads are now unlimited and never expire

  // Ready to download
  return (
    <div className="space-y-2">
      <Button 
        onClick={handleDownload}
        disabled={downloading}
        className={className}
      >
        {downloading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
            Preparing Download...
          </>
        ) : (
          <>
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Download Now
          </>
        )}
      </Button>

      {downloadStatus && (
        <div className="text-xs text-gray-400 text-center">
          Lifetime access - unlimited downloads
        </div>
      )}
    </div>
  )
}
