'use client'

import { useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import Image from 'next/image'
import { 
  XMarkIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'

interface ImageModalProps {
  isOpen: boolean
  onClose: () => void
  images: string[]
  initialIndex?: number
  productName?: string
}

export function ImageModal({ 
  isOpen, 
  onClose, 
  images, 
  initialIndex = 0, 
  productName = 'Product Image' 
}: ImageModalProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [zoom, setZoom] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  useEffect(() => {
    setCurrentIndex(initialIndex)
    setZoom(1)
    setPosition({ x: 0, y: 0 })
  }, [initialIndex, isOpen])

  const nextImage = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1)
      setZoom(1)
      setPosition({ x: 0, y: 0 })
    }
  }

  const prevImage = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
      setZoom(1)
      setPosition({ x: 0, y: 0 })
    }
  }

  const zoomIn = () => {
    setZoom(prev => Math.min(prev * 1.5, 4))
  }

  const zoomOut = () => {
    setZoom(prev => Math.max(prev / 1.5, 1))
    if (zoom <= 1.5) {
      setPosition({ x: 0, y: 0 })
    }
  }

  const resetZoom = () => {
    setZoom(1)
    setPosition({ x: 0, y: 0 })
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom > 1) {
      setIsDragging(true)
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      })
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isOpen) return
    
    switch (e.key) {
      case 'ArrowLeft':
        prevImage()
        break
      case 'ArrowRight':
        nextImage()
        break
      case 'Escape':
        onClose()
        break
      case '+':
      case '=':
        zoomIn()
        break
      case '-':
        zoomOut()
        break
      case '0':
        resetZoom()
        break
    }
  }

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, currentIndex, zoom])

  if (!images || images.length === 0) return null

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/90" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-7xl transform overflow-hidden rounded-2xl bg-gray-900 border border-gray-700 shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-700">
                  <div>
                    <Dialog.Title className="text-lg font-medium text-white">
                      {productName}
                    </Dialog.Title>
                    {images.length > 1 && (
                      <p className="text-sm text-gray-400">
                        {currentIndex + 1} of {images.length}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {/* Zoom Controls */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={zoomOut}
                      disabled={zoom <= 1}
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      <MagnifyingGlassMinusIcon className="w-4 h-4" />
                    </Button>
                    
                    <span className="text-sm text-gray-400 min-w-[3rem] text-center">
                      {Math.round(zoom * 100)}%
                    </span>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={zoomIn}
                      disabled={zoom >= 4}
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      <MagnifyingGlassPlusIcon className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetZoom}
                      disabled={zoom === 1}
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      Reset
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onClose}
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Image Container */}
                <div className="relative bg-black overflow-hidden" style={{ height: '70vh' }}>
                  <div 
                    className="w-full h-full flex items-center justify-center cursor-move"
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                    style={{ cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
                  >
                    <div
                      className="relative transition-transform duration-200"
                      style={{
                        transform: `scale(${zoom}) translate(${position.x / zoom}px, ${position.y / zoom}px)`,
                        transformOrigin: 'center center'
                      }}
                    >
                      <Image
                        src={images[currentIndex]}
                        alt={`${productName} - Image ${currentIndex + 1}`}
                        width={1200}
                        height={800}
                        className="max-w-full max-h-full object-contain"
                        style={{ 
                          maxHeight: '70vh',
                          width: 'auto',
                          height: 'auto'
                        }}
                        quality={100}
                        priority
                      />
                    </div>
                  </div>

                  {/* Navigation Arrows */}
                  {images.length > 1 && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={prevImage}
                        disabled={currentIndex === 0}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 border-gray-600 text-white hover:bg-black/70 disabled:opacity-30"
                      >
                        <ChevronLeftIcon className="w-5 h-5" />
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={nextImage}
                        disabled={currentIndex === images.length - 1}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 border-gray-600 text-white hover:bg-black/70 disabled:opacity-30"
                      >
                        <ChevronRightIcon className="w-5 h-5" />
                      </Button>
                    </>
                  )}
                </div>

                {/* Thumbnail Strip */}
                {images.length > 1 && (
                  <div className="p-4 border-t border-gray-700">
                    <div className="flex space-x-2 overflow-x-auto">
                      {images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            setCurrentIndex(index)
                            setZoom(1)
                            setPosition({ x: 0, y: 0 })
                          }}
                          className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                            currentIndex === index
                              ? 'border-yellow-400'
                              : 'border-gray-600 hover:border-gray-500'
                          }`}
                        >
                          <Image
                            src={image}
                            alt={`Thumbnail ${index + 1}`}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Help Text */}
                <div className="px-4 pb-4">
                  <p className="text-xs text-gray-500 text-center">
                    Use arrow keys to navigate • +/- to zoom • 0 to reset zoom • ESC to close
                  </p>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
