'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon, DocumentTextIcon, CreditCardIcon, UserIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { OrderWithRelations } from '@/types'
import { formatCurrency } from '@/lib/utils'

interface OrderViewModalProps {
  isOpen: boolean
  onClose: () => void
  order: OrderWithRelations | null
}

export function OrderViewModal({ isOpen, onClose, order }: OrderViewModalProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted || !isOpen || !order) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-400 bg-green-400/10'
      case 'PENDING':
        return 'text-yellow-400 bg-yellow-400/10'
      case 'CANCELLED':
        return 'text-red-400 bg-red-400/10'
      default:
        return 'text-gray-400 bg-gray-400/10'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-400 bg-green-400/10'
      case 'PENDING':
        return 'text-yellow-400 bg-yellow-400/10'
      case 'FAILED':
        return 'text-red-400 bg-red-400/10'
      default:
        return 'text-gray-400 bg-gray-400/10'
    }
  }

  return createPortal(
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
        
        <div className="relative bg-gray-900 border border-white/10 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div>
              <h2 className="text-xl font-semibold text-white">Order Details</h2>
              <p className="text-gray-400 text-sm">#{order.orderNumber}</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <XMarkIcon className="h-5 w-5" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Order Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-300">Order Status</span>
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                  {order.status}
                </span>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-300">Payment Status</span>
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                  {order.paymentStatus}
                </span>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-300">Payment Method</span>
                </div>
                <span className="text-white capitalize">{order.paymentMethod}</span>
              </div>
            </div>

            {/* Customer Information */}
            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <UserIcon className="h-5 w-5 text-gray-400" />
                <h3 className="text-lg font-medium text-white">Customer Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-300">Name</label>
                  <p className="text-white">{order.firstName} {order.lastName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-300">Email</label>
                  <p className="text-white">{order.email}</p>
                </div>
                {order.user && (
                  <div>
                    <label className="text-sm font-medium text-gray-300">User ID</label>
                    <p className="text-white font-mono text-sm">{order.user.id}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4">Order Items</h3>
              <div className="space-y-3">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between py-2 border-b border-white/10 last:border-b-0">
                    <div className="flex-1">
                      <h4 className="text-white font-medium">{item.product.name}</h4>
                      <p className="text-gray-400 text-sm">Quantity: {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">{formatCurrency(Number(item.price))}</p>
                      <p className="text-gray-400 text-sm">each</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4">Order Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-300">Subtotal</span>
                  <span className="text-white">{formatCurrency(Number(order.subtotalAmount))}</span>
                </div>
                {order.discountAmount && Number(order.discountAmount) > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-300">Discount</span>
                    <span className="text-green-400">-{formatCurrency(Number(order.discountAmount))}</span>
                  </div>
                )}
                {order.couponCode && (
                  <div className="flex justify-between">
                    <span className="text-gray-300">Coupon Code</span>
                    <span className="text-yellow-400">{order.couponCode}</span>
                  </div>
                )}
                <div className="border-t border-white/10 pt-2">
                  <div className="flex justify-between font-semibold">
                    <span className="text-white">Total</span>
                    <span className="text-white">{formatCurrency(Number(order.totalAmount))}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Dates */}
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4">Order Timeline</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-300">Created At</label>
                  <p className="text-white">{new Date(order.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-300">Updated At</label>
                  <p className="text-white">{new Date(order.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 p-6 border-t border-white/10">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  )
}
