import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    // Get total counts
    const [totalUsers, totalProducts, totalOrders] = await Promise.all([
      prisma.user.count(),
      prisma.product.count(),
      prisma.order.count(),
    ])

    // Get total revenue
    const revenueResult = await prisma.order.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: {
        status: 'COMPLETED',
      },
    })
    const totalRevenue = Number(revenueResult._sum.totalAmount || 0)

    // Get recent orders
    const recentOrdersRaw = await prisma.order.findMany({
      take: 10,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        user: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    })

    // Convert BigInt values in recent orders
    const recentOrders = recentOrdersRaw.map(order => ({
      ...order,
      totalAmount: Number(order.totalAmount),
      subtotalAmount: Number(order.subtotalAmount),
      discountAmount: Number(order.discountAmount),
      items: order.items.map(item => ({
        ...item,
        price: Number(item.price),
      }))
    }))

    // Get top products by sales count
    const topProducts = await prisma.product.findMany({
      take: 10,
      include: {
        _count: {
          select: {
            orderItems: true,
          },
        },
      },
      orderBy: {
        orderItems: {
          _count: 'desc',
        },
      },
    })

    // Transform top products to include sales count and convert BigInt values
    const topProductsWithSales = topProducts.map(product => ({
      ...product,
      price: Number(product.price),
      originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
      salePrice: product.salePrice ? Number(product.salePrice) : null,
      fileSize: product.fileSize ? product.fileSize.toString() : null,
      salesCount: product._count.orderItems,
    }))

    // Get monthly revenue for the last 12 months
    const twelveMonthsAgo = new Date()
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)

    const monthlyRevenueData = await prisma.order.groupBy({
      by: ['createdAt'],
      _sum: {
        totalAmount: true,
      },
      where: {
        status: 'COMPLETED',
        createdAt: {
          gte: twelveMonthsAgo,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    })

    // Process monthly revenue data
    const monthlyRevenue = monthlyRevenueData.reduce((acc: { month: string; revenue: number }[], order) => {
      const month = order.createdAt.toISOString().slice(0, 7) // YYYY-MM format
      const existingMonth = acc.find(item => item.month === month)
      
      if (existingMonth) {
        existingMonth.revenue += Number(order._sum.totalAmount || 0)
      } else {
        acc.push({
          month,
          revenue: Number(order._sum.totalAmount || 0),
        })
      }
      
      return acc
    }, [])

    const dashboardStats = {
      totalUsers,
      totalProducts,
      totalOrders,
      totalRevenue,
      recentOrders,
      topProducts: topProductsWithSales,
      monthlyRevenue,
    }

    return NextResponse.json(dashboardStats)

  } catch (error: any) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch dashboard stats'
    }, { status: 500 })
  }
}
