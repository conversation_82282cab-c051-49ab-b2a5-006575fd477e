'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  CloudArrowUpIcon, 
  DocumentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import Image from 'next/image'
import toast from 'react-hot-toast'

interface ManualPaymentFormProps {
  paymentMethod: any
  orderId: string
  amount: number
  onSuccess: () => void
  onError: (error: string) => void
}

export function ManualPaymentForm({ 
  paymentMethod, 
  orderId, 
  amount, 
  onSuccess, 
  onError 
}: ManualPaymentFormProps) {
  const [transactionHash, setTransactionHash] = useState('')
  const [proofFile, setProofFile] = useState<File | null>(null)
  const [proofPreview, setProofPreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a valid image file (JPEG, PNG, or WebP)')
      return
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      toast.error('File size must be less than 5MB')
      return
    }

    setProofFile(file)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setProofPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Manual payment form submitted')
    console.log('Proof file:', proofFile)
    console.log('Order ID:', orderId)
    console.log('Payment method:', paymentMethod)
    console.log('Transaction hash:', transactionHash)

    if (!proofFile) {
      console.log('No proof file provided')
      toast.error('Please upload payment proof')
      return
    }

    console.log('Starting manual payment submission...')
    setIsSubmitting(true)

    try {
      // Upload proof image
      setIsUploading(true)
      const formData = new FormData()
      formData.append('file', proofFile)

      const uploadResponse = await fetch('/api/upload/payment-proof', {
        method: 'POST',
        body: formData
      })

      const uploadData = await uploadResponse.json()
      setIsUploading(false)

      if (!uploadData.success) {
        throw new Error(uploadData.message || 'Failed to upload proof')
      }

      // Submit manual payment
      const paymentResponse = await fetch('/api/manual-payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          orderId,
          paymentMethod: paymentMethod.name,
          amount,
          currency: paymentMethod.currency,
          walletAddress: paymentMethod.walletAddress,
          transactionHash: transactionHash || undefined,
          proofImageUrl: uploadData.data.url,
          proofFileName: uploadData.data.fileName
        })
      })

      const paymentData = await paymentResponse.json()

      if (paymentData.success) {
        toast.success('Payment proof submitted successfully!')
        onSuccess()
      } else {
        throw new Error(paymentData.message || 'Failed to submit payment')
      }

    } catch (error: any) {
      console.error('Error submitting manual payment:', error)
      onError(error.message || 'Failed to submit payment')
      toast.error(error.message || 'Failed to submit payment')
    } finally {
      setIsSubmitting(false)
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Payment Instructions */}
      <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          Payment Instructions - {paymentMethod.name}
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Wallet Address
            </label>
            <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-600">
              <code className="text-yellow-400 font-mono text-sm break-all">
                {paymentMethod.walletAddress}
              </code>
              <button
                type="button"
                onClick={() => {
                  navigator.clipboard.writeText(paymentMethod.walletAddress)
                  toast.success('Address copied to clipboard!')
                }}
                className="ml-2 text-xs text-blue-400 hover:text-blue-300"
              >
                Copy
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Amount to Send
            </label>
            <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-600">
              <span className="text-yellow-400 font-semibold text-lg">
                {amount.toFixed(2)} {paymentMethod.currency}
              </span>
            </div>
          </div>

          {paymentMethod.instructions && (
            <div className="text-sm text-gray-300">
              <p>{paymentMethod.instructions}</p>
            </div>
          )}

          <div className="bg-yellow-900/20 border border-yellow-700/50 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-200">
                <p className="font-medium mb-1">Important:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Send exactly {amount.toFixed(2)} {paymentMethod.currency} to the address above</li>
                  <li>Take a screenshot of the transaction confirmation</li>
                  <li>Upload the screenshot below as proof of payment</li>
                  <li>Processing time: {paymentMethod.processingTime || '1-24 hours'}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Proof Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Transaction Hash (Optional)
          </label>
          <input
            type="text"
            value={transactionHash}
            onChange={(e) => setTransactionHash(e.target.value)}
            placeholder="Enter transaction hash if available"
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Payment Proof Screenshot *
          </label>
          
          {!proofFile ? (
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-gray-500 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
                id="proof-upload"
              />
              <label htmlFor="proof-upload" className="cursor-pointer">
                <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-2">Click to upload payment proof</p>
                <p className="text-sm text-gray-400">PNG, JPG, WebP up to 5MB</p>
              </label>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <DocumentIcon className="w-8 h-8 text-green-400" />
                  <div className="flex-1">
                    <p className="text-white font-medium">{proofFile.name}</p>
                    <p className="text-sm text-gray-400">
                      {(proofFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setProofFile(null)
                      setProofPreview(null)
                    }}
                    className="text-red-400 hover:text-red-300"
                  >
                    Remove
                  </button>
                </div>
              </div>

              {proofPreview && (
                <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
                  <p className="text-sm text-gray-300 mb-2">Preview:</p>
                  <div className="relative w-full max-w-md mx-auto">
                    <Image
                      src={proofPreview}
                      alt="Payment proof preview"
                      width={400}
                      height={300}
                      className="rounded-lg object-contain"
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <Button
          type="submit"
          disabled={!proofFile || isSubmitting || isUploading}
          variant="premium"
          size="lg"
          className="w-full"
        >
          {isUploading ? (
            <>
              <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
              Uploading Proof...
            </>
          ) : isSubmitting ? (
            <>
              <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
              Submitting Payment...
            </>
          ) : (
            <>
              <CheckCircleIcon className="w-5 h-5 mr-2" />
              Submit Payment Proof
            </>
          )}
        </Button>
      </form>
    </div>
  )
}
