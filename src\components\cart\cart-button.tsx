'use client'

import { useEffect, useState } from 'react'
import { useCartStore } from '@/store/cart-store'
import { ShoppingCartIcon } from '@heroicons/react/24/outline'

export function CartButton() {
  const { getTotalItems, openCart, isHydrated } = useCartStore()
  const [itemCount, setItemCount] = useState(0)

  useEffect(() => {
    if (isHydrated) {
      setItemCount(getTotalItems())
    }
  }, [isHydrated, getTotalItems])

  // Subscribe to cart changes after hydration
  useEffect(() => {
    if (!isHydrated) return

    const unsubscribe = useCartStore.subscribe((state) => {
      setItemCount(state.getTotalItems())
    })

    return unsubscribe
  }, [isHydrated])

  return (
    <button
      onClick={openCart}
      className="text-gray-300 hover:text-white transition-colors duration-200 relative"
    >
      <ShoppingCartIcon className="w-6 h-6" />
      {isHydrated && itemCount > 0 && (
        <span className="absolute -top-2 -right-2 bg-yellow-500 text-black text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold animate-pulse">
          {itemCount > 99 ? '99+' : itemCount}
        </span>
      )}
    </button>
  )
}
