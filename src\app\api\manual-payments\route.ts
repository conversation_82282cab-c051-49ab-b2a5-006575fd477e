import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { requireAdmin } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const submitManualPaymentSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  paymentMethod: z.string().min(1, 'Payment method is required'),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().min(1, 'Currency is required'),
  walletAddress: z.string().min(1, 'Wallet address is required'),
  transactionHash: z.string().optional(),
  proofImageUrl: z.string().min(1, 'Proof image is required'),
  proofFileName: z.string().min(1, 'Proof file name is required')
})

const reviewPaymentSchema = z.object({
  status: z.enum(['APPROVED', 'REJECTED']),
  adminNotes: z.string().optional()
})

// GET /api/manual-payments - Get manual payments (admin only)
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    const where = status ? { status: status as any } : {}

    const [manualPayments, total] = await Promise.all([
      prisma.manualPayment.findMany({
        where,
        include: {
          order: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              },
              items: {
                include: {
                  product: {
                    select: {
                      id: true,
                      name: true,
                      slug: true
                    }
                  }
                }
              }
            }
          },
          reviewer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          }
        },
        orderBy: { submittedAt: 'desc' },
        skip: offset,
        take: limit
      }),
      prisma.manualPayment.count({ where })
    ])

    // Convert Decimal to number for JSON serialization
    const serializedPayments = manualPayments.map(payment => ({
      ...payment,
      amount: Number(payment.amount),
      order: {
        ...payment.order,
        subtotalAmount: Number(payment.order.subtotalAmount),
        discountAmount: Number(payment.order.discountAmount),
        totalAmount: Number(payment.order.totalAmount),
        items: payment.order.items.map(item => ({
          ...item,
          price: Number(item.price)
        }))
      }
    }))

    return NextResponse.json({
      success: true,
      data: serializedPayments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error fetching manual payments:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch manual payments'
    }, { status: 500 })
  }
}

// POST /api/manual-payments - Submit manual payment proof
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const body = await request.json()
    const validatedData = submitManualPaymentSchema.parse(body)

    // Verify order exists
    const order = await prisma.order.findUnique({
      where: { id: validatedData.orderId },
      include: { manualPayments: true }
    })

    if (!order) {
      return NextResponse.json({
        success: false,
        message: 'Order not found'
      }, { status: 404 })
    }

    // Check authorization: user must own the order (no guest orders)
    if (!session?.user?.id || order.userId !== session.user.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized to submit payment for this order'
      }, { status: 403 })
    }

    // Check if manual payment already exists for this order
    if (order.manualPayments.length > 0) {
      return NextResponse.json({
        success: false,
        message: 'Manual payment already submitted for this order'
      }, { status: 400 })
    }

    // Create manual payment record
    const manualPayment = await prisma.manualPayment.create({
      data: {
        orderId: validatedData.orderId,
        paymentMethod: validatedData.paymentMethod,
        amount: validatedData.amount,
        currency: validatedData.currency,
        walletAddress: validatedData.walletAddress,
        transactionHash: validatedData.transactionHash,
        proofImageUrl: validatedData.proofImageUrl,
        proofFileName: validatedData.proofFileName,
        status: 'PENDING'
      }
    })

    // Update order payment method
    await prisma.order.update({
      where: { id: validatedData.orderId },
      data: {
        paymentMethod: validatedData.paymentMethod,
        paymentProofUrl: validatedData.proofImageUrl,
        paymentProofFileName: validatedData.proofFileName,
        manualPaymentAmount: validatedData.amount,
        manualPaymentCurrency: validatedData.currency,
        manualPaymentAddress: validatedData.walletAddress
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Manual payment submitted successfully',
      data: {
        ...manualPayment,
        amount: Number(manualPayment.amount),
        orderId: manualPayment.orderId
      }
    })

  } catch (error) {
    console.error('Error submitting manual payment:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to submit manual payment'
    }, { status: 500 })
  }
}
