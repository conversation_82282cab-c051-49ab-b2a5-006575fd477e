import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  try {
    // Check if USDT payment method already exists
    const existingMethod = await prisma.paymentMethod.findFirst({
      where: { currency: 'USDT' }
    })

    if (existingMethod) {
      console.log('USDT payment method already exists')
      return
    }

    // Create USDT payment method
    const usdtMethod = await prisma.paymentMethod.create({
      data: {
        name: 'USDT (TRC20)',
        type: 'MANUAL',
        currency: 'USDT',
        walletAddress: 'TYourWalletAddressHere123456789',
        discountPercentage: 20.00,
        isActive: true,
        instructions: 'Send USDT to the wallet address above and upload a screenshot of the transaction as proof. You will receive a 20% discount on your order total.',
        processingTime: '1-24 hours',
        sortOrder: 0
      }
    })

    console.log('USDT payment method created:', usdtMethod)
  } catch (error) {
    console.error('Error creating payment method:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
