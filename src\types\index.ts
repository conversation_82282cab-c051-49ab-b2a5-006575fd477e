import { 
  User, 
  Product, 
  Category, 
  Order, 
  OrderItem, 
  Payment, 
  Download, 
  CartItem, 
  WishlistItem, 
  Review, 
  Membership,
  UserRole,
  ProductStatus,
  OrderStatus,
  PaymentStatus,
  MembershipType,
  MembershipStatus
} from '@prisma/client'

// Extended types with relations
export type UserWithRelations = User & {
  orders?: Order[]
  downloads?: Download[]
  reviews?: Review[]
  membership?: Membership | null
  cart?: CartItem[]
  wishlist?: WishlistItem[]
}

export type ProductWithRelations = Product & {
  category: Category
  reviews?: Review[]
  _count?: {
    reviews: number
    downloads: number
  }
  averageRating?: number
}

export type OrderWithRelations = Order & {
  user: User | null
  items: (OrderItem & {
    product: Product
  })[]
  payments?: Payment[]
}

export type CartItemWithProduct = CartItem & {
  product: ProductWithRelations
}

export type WishlistItemWithProduct = WishlistItem & {
  product: ProductWithRelations
}

export type ReviewWithUser = Review & {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'avatar'>
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form types
export interface LoginForm {
  email: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  acceptTerms: boolean
}

export interface ProductForm {
  name: string
  slug: string
  description: string
  shortDescription: string
  price: number
  originalPrice?: number
  isOnSale: boolean
  salePrice?: number
  categoryId: string
  status: ProductStatus
  featured: boolean
  tags: string[]
  metaTitle?: string
  metaDescription?: string
  images: string[]
}

export interface CheckoutForm {
  email: string
  firstName: string
  lastName: string
  paymentMethod: 'stripe' | 'paypal'
  acceptTerms: boolean
}

// Search and filter types
export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  featured?: boolean
  onSale?: boolean
  tags?: string[]
  search?: string
  sortBy?: 'name' | 'price' | 'createdAt' | 'rating'
  sortOrder?: 'asc' | 'desc'
}

export interface SearchParams {
  q?: string
  category?: string
  page?: string
  limit?: string
  sort?: string
  order?: string
  minPrice?: string
  maxPrice?: string
  featured?: string
  onSale?: string
}

// Payment types
export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: string
  clientSecret?: string
}

export interface StripePaymentData {
  paymentIntentId: string
  paymentMethodId?: string
  amount: number
  currency: string
  orderId: string
}

// File upload types
export interface FileUpload {
  file: File
  fileName: string
  fileSize: number
  fileType: string
}

export interface UploadedFile {
  id: string
  fileName: string
  fileUrl: string
  fileSize: number
  fileType: string
}

// Membership types
export interface MembershipPlan {
  type: MembershipType
  name: string
  description: string
  price: number
  duration: number // in days
  features: string[]
  stripePriceId?: string
}

// Analytics types
export interface DashboardStats {
  totalUsers: number
  totalProducts: number
  totalOrders: number
  totalRevenue: number
  recentOrders: OrderWithRelations[]
  topProducts: (ProductWithRelations & { salesCount: number })[]
  monthlyRevenue: { month: string; revenue: number }[]
}

// Download types
export interface DownloadLink {
  id: string
  url: string
  expiresAt: Date
  downloadCount: number
  maxDownloads?: number
}

// Cart types
export interface CartSummary {
  items: CartItemWithProduct[]
  subtotal: number
  total: number
  itemCount: number
}

// Notification types
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: Date
  read: boolean
}

// Email types
export interface EmailTemplate {
  to: string
  subject: string
  template: string
  data: Record<string, any>
}

// Export Prisma types
export {
  User,
  Product,
  Category,
  Order,
  OrderItem,
  Payment,
  Download,
  CartItem,
  WishlistItem,
  Review,
  Membership,
  UserRole,
  ProductStatus,
  OrderStatus,
  PaymentStatus,
  MembershipType,
  MembershipStatus,
}
