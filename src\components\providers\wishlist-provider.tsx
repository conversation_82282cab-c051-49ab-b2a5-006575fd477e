'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useWishlistStore } from '@/store/wishlist-store'

interface WishlistProviderProps {
  children: React.ReactNode
}

export function WishlistProvider({ children }: WishlistProviderProps) {
  const { data: session, status } = useSession()
  const { fetchWishlist, clearWishlist, setHydrated, isHydrated } = useWishlistStore()

  // Initialize wishlist when session changes
  useEffect(() => {
    if (status === 'loading') return

    if (status === 'authenticated' && session?.user) {
      // User is authenticated, fetch their wishlist
      fetchWishlist()
    } else if (status === 'unauthenticated') {
      // User is not authenticated, clear wishlist
      clearWishlist()
    }
  }, [status, session?.user, fetchWishlist, clearWishlist])

  // Set hydrated state
  useEffect(() => {
    if (!isHydrated) {
      setHydrated()
    }
  }, [isHydrated, setHydrated])

  return <>{children}</>
}
