'use client'

import { useSession } from 'next-auth/react'
import { useWishlistStore } from '@/store/wishlist-store'
import { Button } from '@/components/ui/button'
import { HeartIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'
import toast from 'react-hot-toast'
import { cn } from '@/lib/utils'

interface WishlistButtonProps {
  productId: string
  variant?: 'default' | 'icon' | 'glass'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showText?: boolean
}

export function WishlistButton({ 
  productId, 
  variant = 'default', 
  size = 'md',
  className = '',
  showText = true
}: WishlistButtonProps) {
  const { data: session } = useSession()
  const { hasItem: isWishlisted, toggleItem: toggleWishlist, isHydrated } = useWishlistStore()

  const handleToggleWishlist = async () => {
    if (!session) {
      toast.error('Please sign in to add items to wishlist')
      return
    }
    await toggleWishlist(productId)
  }

  if (!isHydrated) {
    return (
      <Button
        variant={variant as any}
        size={size}
        className={cn('opacity-50', className)}
        disabled
      >
        <HeartIcon className="w-4 h-4" />
        {showText && <span className="ml-2">Wishlist</span>}
      </Button>
    )
  }

  const isInWishlist = isWishlisted(productId)

  return (
    <Button
      variant={variant as any}
      size={size}
      className={cn(
        'transition-colors duration-200',
        isInWishlist && 'text-red-500 hover:text-red-600',
        className
      )}
      onClick={handleToggleWishlist}
    >
      {isInWishlist ? (
        <HeartSolidIcon className="w-4 h-4 text-red-500" />
      ) : (
        <HeartIcon className="w-4 h-4" />
      )}
      {showText && (
        <span className="ml-2">
          {isInWishlist ? 'In Wishlist' : 'Wishlist'}
        </span>
      )}
    </Button>
  )
}
