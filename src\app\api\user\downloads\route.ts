import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/user/downloads - Get user's purchased products and download tokens
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    // Get all completed purchases for the user
    const purchases = await prisma.purchase.findMany({
      where: {
        userId: session.user.id,
        status: 'COMPLETED'
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            images: true,
            fileKey: true,
            fileName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log(`User ${session.user.id} has ${purchases.length} completed purchases:`, purchases.map(p => ({ id: p.id, productId: p.productId, productName: p.product.name })))

    // For each purchase, check if there's an active download token
    const downloadsWithTokens = await Promise.all(
      purchases.map(async (purchase) => {
        const downloadToken = await prisma.downloadToken.findFirst({
          where: {
            userId: session.user.id,
            productId: purchase.productId,
            isActive: true,
            expiresAt: {
              gt: new Date()
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        })

        return {
          id: purchase.id,
          productId: purchase.productId,
          product: purchase.product,
          purchaseDate: purchase.createdAt,
          hasToken: !!downloadToken,
          token: downloadToken?.token || null,
          downloadCount: downloadToken ? Number(downloadToken.downloadCount) : 0,
          maxDownloads: downloadToken ? Number(downloadToken.maxDownloads) : 5,
          expiresAt: downloadToken?.expiresAt || null,
          isActive: downloadToken?.isActive || false,
          createdAt: purchase.createdAt
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: downloadsWithTokens
    })

  } catch (error) {
    console.error('Error fetching user downloads:', error)
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}
