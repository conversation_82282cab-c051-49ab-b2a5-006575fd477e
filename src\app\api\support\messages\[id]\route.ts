import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { z } from 'zod'
import { SupportMessageStatus, SupportMessagePriority } from '@prisma/client'

interface RouteParams {
  params: {
    id: string
  }
}

const updateSupportMessageSchema = z.object({
  status: z.nativeEnum(SupportMessageStatus).optional(),
  priority: z.nativeEnum(SupportMessagePriority).optional(),
  adminNotes: z.string().optional(),
})

const addResponseSchema = z.object({
  content: z.string().min(1, 'Response content is required').max(5000, 'Response is too long'),
})

// GET /api/support/messages/[id] - Get a specific support message
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { id } = params

    const message = await prisma.supportMessage.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        responses: {
          include: {
            admin: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    })

    if (!message) {
      return NextResponse.json({
        success: false,
        message: 'Support message not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: message
    })

  } catch (error: any) {
    console.error('Error fetching support message:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch support message'
    }, { status: 500 })
  }
}

// PATCH /api/support/messages/[id] - Update support message status/priority/notes
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()

    // Validate the request body
    const validatedData = updateSupportMessageSchema.parse(body)

    // Check if message exists
    const existingMessage = await prisma.supportMessage.findUnique({
      where: { id }
    })

    if (!existingMessage) {
      return NextResponse.json({
        success: false,
        message: 'Support message not found'
      }, { status: 404 })
    }

    // Update the message
    const updatedMessage = await prisma.supportMessage.update({
      where: { id },
      data: {
        ...validatedData,
        respondedAt: validatedData.status === SupportMessageStatus.RESOLVED || 
                    validatedData.status === SupportMessageStatus.CLOSED 
                    ? new Date() 
                    : existingMessage.respondedAt,
        respondedBy: validatedData.status === SupportMessageStatus.RESOLVED || 
                    validatedData.status === SupportMessageStatus.CLOSED 
                    ? session.user.id 
                    : existingMessage.respondedBy,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        responses: {
          include: {
            admin: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    })

    // Send status update notification email to user if status changed
    if (validatedData.status && validatedData.status !== existingMessage.status) {
      try {
        const { sendSupportReplyEmail } = await import('@/lib/email')
        const customerName = updatedMessage.user
          ? `${updatedMessage.user.firstName || ''} ${updatedMessage.user.lastName || ''}`.trim() || updatedMessage.name
          : updatedMessage.name

        const adminName = session.user.firstName && session.user.lastName
          ? `${session.user.firstName} ${session.user.lastName}`
          : 'Support Team'

        const statusMessages = {
          IN_PROGRESS: 'Your support request is now being reviewed by our team.',
          RESOLVED: 'Your support request has been resolved. Please review our response.',
          CLOSED: 'Your support request has been closed. Thank you for contacting us.'
        }

        const statusMessage = statusMessages[validatedData.status as keyof typeof statusMessages] ||
          `Your support request status has been updated to ${validatedData.status}.`

        await sendSupportReplyEmail({
          customerName,
          email: updatedMessage.email,
          ticketId: updatedMessage.id,
          subject: updatedMessage.subject,
          status: updatedMessage.status,
          adminName,
          responseContent: statusMessage + (validatedData.adminNotes ? `\n\nAdmin Notes: ${validatedData.adminNotes}` : ''),
          responseDate: new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }),
          supportUrl: `${process.env.SITE_URL || 'https://forexbotzone.com'}/support`
        })
      } catch (emailError) {
        console.error('Failed to send support status update email:', emailError)
        // Don't fail the request if email fails
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Support message updated successfully',
      data: updatedMessage
    })

  } catch (error: any) {
    console.error('Error updating support message:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update support message'
    }, { status: 500 })
  }
}

// POST /api/support/messages/[id] - Add a response to support message
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()

    // Validate the request body
    const validatedData = addResponseSchema.parse(body)

    // Check if message exists
    const existingMessage = await prisma.supportMessage.findUnique({
      where: { id }
    })

    if (!existingMessage) {
      return NextResponse.json({
        success: false,
        message: 'Support message not found'
      }, { status: 404 })
    }

    // Create the response
    const response = await prisma.supportResponse.create({
      data: {
        messageId: id,
        content: validatedData.content,
        isAdmin: true,
        adminId: session.user.id,
      },
      include: {
        admin: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        }
      }
    })

    // Update message status to IN_PROGRESS if it was OPEN
    let updatedMessage = existingMessage
    if (existingMessage.status === SupportMessageStatus.OPEN) {
      updatedMessage = await prisma.supportMessage.update({
        where: { id },
        data: {
          status: SupportMessageStatus.IN_PROGRESS,
          respondedAt: new Date(),
          respondedBy: session.user.id,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        }
      })
    }

    // Send reply notification email to user
    try {
      const { sendSupportReplyEmail } = await import('@/lib/email')
      const customerName = updatedMessage.user
        ? `${updatedMessage.user.firstName || ''} ${updatedMessage.user.lastName || ''}`.trim() || updatedMessage.name
        : updatedMessage.name

      const adminName = response.admin
        ? `${response.admin.firstName || ''} ${response.admin.lastName || ''}`.trim() || 'Support Team'
        : 'Support Team'

      await sendSupportReplyEmail({
        customerName,
        email: updatedMessage.email,
        ticketId: updatedMessage.id,
        subject: updatedMessage.subject,
        status: updatedMessage.status,
        adminName,
        responseContent: validatedData.content,
        responseDate: response.createdAt.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        supportUrl: `${process.env.SITE_URL || 'https://forexbotzone.com'}/support`
      })
    } catch (emailError) {
      console.error('Failed to send support reply email:', emailError)
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Response added successfully',
      data: response
    })

  } catch (error: any) {
    console.error('Error adding support response:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to add response'
    }, { status: 500 })
  }
}
