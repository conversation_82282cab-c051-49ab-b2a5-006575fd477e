'use client'

import { useState, useEffect } from 'react'
import { XMarkIcon, EyeIcon, CodeBracketIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { EmailTemplateType } from '@prisma/client'
import { toast } from 'react-hot-toast'

interface EmailTemplate {
  id: string
  name: string
  subject: string
  htmlContent: string
  textContent?: string
  type: EmailTemplateType
  variables?: Record<string, string>
  isActive: boolean
}

interface EmailTemplateEditorProps {
  isOpen: boolean
  onClose: () => void
  onSave: (template: EmailTemplate) => void
  template: EmailTemplate | null
  isNew?: boolean
}

export function EmailTemplateEditor({
  isOpen,
  onClose,
  onSave,
  template,
  isNew = false
}: EmailTemplateEditorProps) {
  const [loading, setLoading] = useState(false)
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('edit')
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    htmlContent: '',
    textContent: '',
    type: EmailTemplateType.CUSTOM,
    isActive: true,
    variables: {} as Record<string, string>
  })

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent || '',
        type: template.type,
        isActive: template.isActive,
        variables: template.variables || {}
      })
    } else if (isNew) {
      setFormData({
        name: '',
        subject: '',
        htmlContent: getDefaultHtmlTemplate(),
        textContent: '',
        type: EmailTemplateType.CUSTOM,
        isActive: true,
        variables: {}
      })
    }
  }, [template, isNew])

  const getDefaultHtmlTemplate = () => `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{subject}}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
    <h1 style="color: #fbbf24; margin: 0; font-size: 28px;">Forex Bot Zone</h1>
  </div>
  
  <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
    <h2 style="color: #1f2937; margin-top: 0;">Hello {{firstName}}!</h2>
    
    <p>Your email content goes here...</p>
    
    <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
    
    <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
      © 2024 Forex Bot Zone. All rights reserved.
    </p>
  </div>
</body>
</html>
  `.trim()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const endpoint = isNew ? '/api/admin/email-templates' : `/api/admin/email-templates/${template?.id}`
      const method = isNew ? 'POST' : 'PUT'

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(`Template ${isNew ? 'created' : 'updated'} successfully!`)
        onSave(data.data)
        onClose()
      } else {
        toast.error(data.message || `Failed to ${isNew ? 'create' : 'update'} template`)
      }
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error(`Failed to ${isNew ? 'create' : 'update'} template`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      subject: '',
      htmlContent: '',
      textContent: '',
      type: EmailTemplateType.CUSTOM,
      isActive: true,
      variables: {}
    })
    setViewMode('edit')
    onClose()
  }

  const renderPreview = () => {
    const sampleVariables = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      subject: formData.subject || 'Sample Subject',
      loginUrl: 'https://example.com/login',
      resetUrl: 'https://example.com/reset',
      expiresIn: '24 hours',
      orderNumber: 'ORD-12345',
      total: '99.99',
      customerName: 'John Doe',
      downloadUrl: 'https://example.com/download',
      companyName: 'Forex Bot Zone',
      supportEmail: '<EMAIL>',
      currentYear: new Date().getFullYear().toString()
    }

    let previewHtml = formData.htmlContent

    // Replace all variables
    for (const [key, value] of Object.entries(sampleVariables)) {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
      previewHtml = previewHtml.replace(regex, String(value))
    }

    // Handle any remaining unreplaced variables by showing them as placeholders
    previewHtml = previewHtml.replace(/\{\{([^}]+)\}\}/g, '<span style="background: #fef3c7; color: #92400e; padding: 2px 4px; border-radius: 3px; font-size: 12px;">{{$1}}</span>')

    // Remove any script tags to prevent JavaScript execution
    previewHtml = previewHtml.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')

    // Remove any event handlers
    previewHtml = previewHtml.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')

    // Create a safe HTML document for preview
    const safeHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>Email Preview</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              background-color: #f5f5f5;
            }
            .email-container {
              max-width: 600px;
              margin: 0 auto;
              background-color: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
          </style>
        </head>
        <body>
          <div class="email-container">
            ${previewHtml}
          </div>
        </body>
      </html>
    `

    return (
      <div className="border border-gray-600 rounded-lg overflow-hidden">
        <iframe
          srcDoc={safeHtml}
          className="w-full h-96"
          title="Email Preview"
          sandbox=""
          onError={(e) => {
            console.error('Preview iframe error:', e)
          }}
        />
      </div>
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />
        
        {/* Modal */}
        <div className="relative transform overflow-hidden rounded-lg bg-gray-800 border border-gray-700 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">
              {isNew ? 'Create Email Template' : 'Edit Email Template'}
            </h3>
            
            <div className="flex items-center space-x-2">
              <div className="flex bg-gray-700 rounded-lg p-1">
                <button
                  type="button"
                  onClick={() => setViewMode('edit')}
                  className={`px-3 py-1 text-sm rounded ${
                    viewMode === 'edit' 
                      ? 'bg-yellow-500 text-black' 
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  <CodeBracketIcon className="h-4 w-4 inline mr-1" />
                  Edit
                </button>
                <button
                  type="button"
                  onClick={() => setViewMode('preview')}
                  className={`px-3 py-1 text-sm rounded ${
                    viewMode === 'preview' 
                      ? 'bg-yellow-500 text-black' 
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  <EyeIcon className="h-4 w-4 inline mr-1" />
                  Preview
                </button>
              </div>
              
              <button
                type="button"
                className="rounded-md text-gray-400 hover:text-gray-300 focus:outline-none"
                onClick={handleClose}
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[80vh] overflow-y-auto">
            {viewMode === 'edit' ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-white">Template Name</Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="mt-1 bg-gray-700 border-gray-600 text-white"
                      disabled={loading}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="type" className="text-white">Template Type</Label>
                    <select
                      id="type"
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      disabled={loading}
                      className="mt-1 w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                    >
                      <option value={EmailTemplateType.WELCOME}>Welcome</option>
                      <option value={EmailTemplateType.ORDER_CONFIRMATION}>Order Confirmation</option>
                      <option value={EmailTemplateType.PASSWORD_RESET}>Password Reset</option>
                      <option value="SUPPORT_CONFIRMATION">Support Confirmation</option>
                      <option value="SUPPORT_REPLY">Support Reply</option>
                      <option value={EmailTemplateType.CUSTOM}>Custom</option>
                    </select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="subject" className="text-white">Subject Line</Label>
                  <Input
                    id="subject"
                    name="subject"
                    type="text"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="mt-1 bg-gray-700 border-gray-600 text-white"
                    disabled={loading}
                    placeholder="Use {{variables}} for dynamic content"
                  />
                </div>

                <div>
                  <Label htmlFor="htmlContent" className="text-white">HTML Content</Label>
                  <Textarea
                    id="htmlContent"
                    name="htmlContent"
                    value={formData.htmlContent}
                    onChange={handleInputChange}
                    required
                    rows={15}
                    className="mt-1 bg-gray-700 border-gray-600 text-white font-mono text-sm"
                    disabled={loading}
                    placeholder="HTML email template with {{variables}}"
                  />
                </div>

                <div>
                  <Label htmlFor="textContent" className="text-white">Text Content (Optional)</Label>
                  <Textarea
                    id="textContent"
                    name="textContent"
                    value={formData.textContent}
                    onChange={handleInputChange}
                    rows={8}
                    className="mt-1 bg-gray-700 border-gray-600 text-white"
                    disabled={loading}
                    placeholder="Plain text version of the email"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    disabled={loading}
                    className="rounded border-gray-600 text-yellow-400 focus:ring-yellow-400"
                  />
                  <Label htmlFor="isActive" className="ml-2 text-white">Active Template</Label>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading}
                    className="bg-yellow-500 hover:bg-yellow-600 text-black"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                        {isNew ? 'Creating...' : 'Updating...'}
                      </>
                    ) : (
                      isNew ? 'Create Template' : 'Update Template'
                    )}
                  </Button>
                </div>
              </form>
            ) : (
              <div>
                <h4 className="text-white text-lg mb-4">Email Preview</h4>
                <p className="text-gray-400 text-sm mb-4">
                  Preview with sample data. Variables like {{firstName}} are replaced with example values.
                </p>
                {renderPreview()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
