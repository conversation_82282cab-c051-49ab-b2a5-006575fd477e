import { Resend } from 'resend'
import { prisma } from '@/lib/prisma'
import { EmailStatus, EmailTemplateType } from '@prisma/client'

const resend = new Resend(process.env.RESEND_API_KEY)

export interface EmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  from?: string
  replyTo?: string
}

export interface WelcomeEmailData {
  firstName: string
  email: string
  loginUrl: string
}

export interface OrderConfirmationData {
  orderNumber: string
  customerName: string
  items: Array<{
    name: string
    price: number
    quantity: number
  }>
  total: number
  downloadUrl?: string
}

export interface PasswordResetData {
  firstName: string
  resetUrl: string
  expiresIn: string
}

export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

export interface SupportMessageData {
  id: string
  name: string
  email: string
  subject: string
  message: string
  isGuest: boolean
  priority: string
  createdAt: Date
}

/**
 * Log email to database
 */
async function logEmail(
  to: string,
  subject: string,
  htmlContent?: string,
  textContent?: string,
  templateId?: string,
  status: EmailStatus = EmailStatus.PENDING,
  metadata?: any
) {
  try {
    return await prisma.emailLog.create({
      data: {
        to: Array.isArray(to) ? to.join(', ') : to,
        from: 'Forex Bot Zone <<EMAIL>>',
        subject,
        htmlContent,
        textContent,
        templateId,
        status,
        metadata,
        sentAt: status === EmailStatus.SENT ? new Date() : null
      }
    })
  } catch (error) {
    console.error('Error logging email:', error)
    return null
  }
}

/**
 * Update email log status
 */
async function updateEmailLogStatus(logId: string, status: EmailStatus, timestamp?: Date) {
  try {
    const updateData: any = { status }

    switch (status) {
      case EmailStatus.SENT:
        updateData.sentAt = timestamp || new Date()
        break
      case EmailStatus.DELIVERED:
        updateData.deliveredAt = timestamp || new Date()
        break
      case EmailStatus.OPENED:
        updateData.openedAt = timestamp || new Date()
        break
      case EmailStatus.CLICKED:
        updateData.clickedAt = timestamp || new Date()
        break
      case EmailStatus.BOUNCED:
        updateData.bouncedAt = timestamp || new Date()
        break
      case EmailStatus.FAILED:
        updateData.failedAt = timestamp || new Date()
        break
    }

    await prisma.emailLog.update({
      where: { id: logId },
      data: updateData
    })
  } catch (error) {
    console.error('Error updating email log status:', error)
  }
}

/**
 * Send a generic email
 */
export async function sendEmail(options: EmailOptions, templateId?: string) {
  let emailLog: any = null

  try {
    // Log email before sending
    emailLog = await logEmail(
      Array.isArray(options.to) ? options.to.join(', ') : options.to,
      options.subject,
      options.html,
      options.text,
      templateId,
      EmailStatus.PENDING
    )

    const { data, error } = await resend.emails.send({
      from: options.from || 'Forex Bot Zone <<EMAIL>>',
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      replyTo: options.replyTo,
    })

    if (error) {
      console.error('Email sending error:', error)
      if (emailLog) {
        await updateEmailLogStatus(emailLog.id, EmailStatus.FAILED)
      }
      throw new Error(`Failed to send email: ${error.message}`)
    }

    // Update log status to sent
    if (emailLog) {
      await updateEmailLogStatus(emailLog.id, EmailStatus.SENT)
    }

    return { success: true, data, logId: emailLog?.id }
  } catch (error) {
    console.error('Email service error:', error)
    if (emailLog) {
      await updateEmailLogStatus(emailLog.id, EmailStatus.FAILED)
    }
    throw error
  }
}

/**
 * Render email template with variables
 */
function renderTemplate(template: string, variables: Record<string, any>): string {
  let rendered = template
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
    rendered = rendered.replace(regex, String(value))
  }
  return rendered
}

/**
 * Send email using template
 */
export async function sendTemplateEmail(
  to: string,
  templateType: EmailTemplateType,
  variables: Record<string, any>
) {
  try {
    const template = await prisma.emailTemplate.findFirst({
      where: { type: templateType, isActive: true }
    })

    if (!template) {
      throw new Error(`No active template found for type: ${templateType}`)
    }

    const htmlContent = renderTemplate(template.htmlContent, variables)
    const textContent = template.textContent ? renderTemplate(template.textContent, variables) : undefined
    const subject = renderTemplate(template.subject, variables)

    return await sendEmail({
      to,
      subject,
      html: htmlContent,
      text: textContent
    }, template.id)

  } catch (error) {
    console.error('Error sending template email:', error)
    throw error
  }
}

/**
 * Send welcome email to new users
 */
export async function sendWelcomeEmail(to: string, data: WelcomeEmailData) {
  try {
    return await sendTemplateEmail(to, EmailTemplateType.WELCOME, {
      firstName: data.firstName,
      email: data.email,
      loginUrl: data.loginUrl
    })
  } catch (error) {
    console.error('Error sending welcome email:', error)
    // Fallback to hardcoded template if database template fails
    return await sendWelcomeEmailFallback(to, data)
  }
}

/**
 * Fallback welcome email (original hardcoded version)
 */
async function sendWelcomeEmailFallback(to: string, data: WelcomeEmailData) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Forex Bot Zone</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fbbf24; margin: 0; font-size: 28px;">Welcome to Forex Bot Zone!</h1>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1f2937; margin-top: 0;">Hello ${data.firstName}!</h2>
        
        <p>Thank you for joining Forex Bot Zone, your premier destination for professional forex trading tools and expert advisors.</p>
        
        <p>Your account has been successfully created with the email: <strong>${data.email}</strong></p>
        
        <div style="background: #fff; padding: 20px; border-radius: 8px; border-left: 4px solid #fbbf24; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">What's Next?</h3>
          <ul style="margin: 0; padding-left: 20px;">
            <li>Browse our premium collection of Expert Advisors</li>
            <li>Explore professional trading indicators</li>
            <li>Access exclusive trading strategies</li>
            <li>Join our community of successful traders</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.loginUrl}" style="background: #fbbf24; color: #1f2937; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            Access Your Account
          </a>
        </div>
        
        <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
          If you have any questions, feel free to contact our support team. We're here to help you succeed in your trading journey!
        </p>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #6b7280; font-size: 12px;">
        <p>© 2024 Forex Bot Zone. All rights reserved.</p>
        <p>This email was sent to ${data.email}</p>
      </div>
    </body>
    </html>
  `

  const text = `
    Welcome to Forex Bot Zone!
    
    Hello ${data.firstName}!
    
    Thank you for joining Forex Bot Zone, your premier destination for professional forex trading tools and expert advisors.
    
    Your account has been successfully created with the email: ${data.email}
    
    What's Next?
    - Browse our premium collection of Expert Advisors
    - Explore professional trading indicators
    - Access exclusive trading strategies
    - Join our community of successful traders
    
    Access your account: ${data.loginUrl}
    
    If you have any questions, feel free to contact our support team.
    
    © 2024 Forex Bot Zone. All rights reserved.
  `

  return sendEmail({
    to,
    subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
    html,
    text,
  })
}

/**
 * Send order confirmation email
 */
export async function sendOrderConfirmationEmail(to: string, data: OrderConfirmationData) {
  const itemsHtml = data.items.map(item => `
    <tr>
      <td style="padding: 10px; border-bottom: 1px solid #e5e7eb;">${item.name}</td>
      <td style="padding: 10px; border-bottom: 1px solid #e5e7eb; text-align: center;">${item.quantity}</td>
      <td style="padding: 10px; border-bottom: 1px solid #e5e7eb; text-align: right;">$${item.price.toFixed(2)}</td>
    </tr>
  `).join('')

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Order Confirmation - ${data.orderNumber}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #1f2937 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fff; margin: 0; font-size: 28px;">Order Confirmed!</h1>
        <p style="color: #d1fae5; margin: 10px 0 0 0;">Order #${data.orderNumber}</p>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1f2937; margin-top: 0;">Thank you, ${data.customerName}!</h2>
        
        <p>Your order has been successfully processed and confirmed. You can now download your products immediately.</p>
        
        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">Order Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background: #f3f4f6;">
                <th style="padding: 10px; text-align: left; border-bottom: 2px solid #e5e7eb;">Product</th>
                <th style="padding: 10px; text-align: center; border-bottom: 2px solid #e5e7eb;">Qty</th>
                <th style="padding: 10px; text-align: right; border-bottom: 2px solid #e5e7eb;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr style="background: #f9fafb; font-weight: bold;">
                <td style="padding: 15px; border-top: 2px solid #e5e7eb;" colspan="2">Total</td>
                <td style="padding: 15px; border-top: 2px solid #e5e7eb; text-align: right;">$${data.total.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        ${data.downloadUrl ? `
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.downloadUrl}" style="background: #10b981; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            Download Your Products
          </a>
        </div>
        ` : ''}
        
        <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0;">
          <p style="margin: 0; color: #92400e;"><strong>Important:</strong> Please save your download links. They will expire in 30 days and have a download limit of 5 times per product.</p>
        </div>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #6b7280; font-size: 12px;">
        <p>© 2024 Forex Bot Zone. All rights reserved.</p>
      </div>
    </body>
    </html>
  `

  return sendEmail({
    to,
    subject: `Order Confirmation - ${data.orderNumber} | Forex Bot Zone`,
    html,
  })
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(to: string, data: PasswordResetData) {
  try {
    return await sendTemplateEmail(to, EmailTemplateType.PASSWORD_RESET, {
      firstName: data.firstName,
      resetUrl: data.resetUrl,
      expiresIn: data.expiresIn
    })
  } catch (error) {
    console.error('Error sending password reset email:', error)
    // Fallback to hardcoded template if database template fails
    return await sendPasswordResetEmailFallback(to, data)
  }
}

/**
 * Fallback password reset email (original hardcoded version)
 */
async function sendPasswordResetEmailFallback(to: string, data: PasswordResetData) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - Forex Bot Zone</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #1f2937 0%, #ef4444 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fff; margin: 0; font-size: 28px;">Password Reset</h1>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1f2937; margin-top: 0;">Hello ${data.firstName}!</h2>
        
        <p>We received a request to reset your password for your Forex Bot Zone account.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.resetUrl}" style="background: #ef4444; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            Reset Your Password
          </a>
        </div>
        
        <div style="background: #fef2f2; padding: 15px; border-radius: 8px; border-left: 4px solid #ef4444; margin: 20px 0;">
          <p style="margin: 0; color: #991b1b;"><strong>Security Notice:</strong> This link will expire in ${data.expiresIn}. If you didn't request this reset, please ignore this email.</p>
        </div>
        
        <p style="color: #6b7280; font-size: 14px;">
          If the button doesn't work, copy and paste this link into your browser:<br>
          <a href="${data.resetUrl}" style="color: #3b82f6; word-break: break-all;">${data.resetUrl}</a>
        </p>
      </div>
      
      <div style="text-align: center; padding: 20px; color: #6b7280; font-size: 12px;">
        <p>© 2024 Forex Bot Zone. All rights reserved.</p>
      </div>
    </body>
    </html>
  `

  return sendEmail({
    to,
    subject: 'Reset Your Password - Forex Bot Zone',
    html,
  })
}

/**
 * Send contact form notification to admin
 */
export async function sendContactFormNotification(data: ContactFormData) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Contact Form Submission</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: #1f2937; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fbbf24; margin: 0;">New Contact Form Submission</h1>
      </div>
      
      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1f2937; margin-top: 0;">Contact Details</h2>
        
        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${data.name}</p>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
        </div>
        
        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">Message</h3>
          <p style="white-space: pre-wrap;">${data.message}</p>
        </div>
      </div>
    </body>
    </html>
  `

  return sendEmail({
    to: '<EMAIL>',
    subject: `New Contact: ${data.subject}`,
    html,
    replyTo: data.email,
  })
}

/**
 * Send support confirmation email to user
 */
export async function sendSupportConfirmationEmail(data: {
  customerName: string
  email: string
  ticketId: string
  subject: string
  priority: string
  message: string
  submittedDate: string
}) {
  const priorityColors = {
    LOW: '#10b981',
    NORMAL: '#3b82f6',
    HIGH: '#f59e0b',
    URGENT: '#ef4444'
  }

  const priorityColor = priorityColors[data.priority as keyof typeof priorityColors] || '#3b82f6'

  try {
    return await sendTemplateEmail(data.email, 'SUPPORT_CONFIRMATION' as any, {
      customerName: data.customerName,
      ticketId: data.ticketId,
      subject: data.subject,
      priority: data.priority,
      priorityColor: priorityColor,
      submittedDate: data.submittedDate,
      message: data.message
    })
  } catch (error) {
    console.error('Error sending support confirmation email:', error)
    // Fallback to hardcoded template
    return await sendSupportConfirmationEmailFallback(data)
  }
}

/**
 * Send support reply notification email to user
 */
export async function sendSupportReplyEmail(data: {
  customerName: string
  email: string
  ticketId: string
  subject: string
  status: string
  adminName: string
  responseContent: string
  responseDate: string
  supportUrl: string
}) {
  const statusColors = {
    OPEN: '#3b82f6',
    IN_PROGRESS: '#f59e0b',
    RESOLVED: '#10b981',
    CLOSED: '#6b7280'
  }

  const statusColor = statusColors[data.status as keyof typeof statusColors] || '#3b82f6'

  try {
    return await sendTemplateEmail(data.email, 'SUPPORT_REPLY' as any, {
      customerName: data.customerName,
      ticketId: data.ticketId,
      subject: data.subject,
      status: data.status,
      statusColor: statusColor,
      adminName: data.adminName,
      responseContent: data.responseContent,
      responseDate: data.responseDate,
      supportUrl: data.supportUrl
    })
  } catch (error) {
    console.error('Error sending support reply email:', error)
    // Fallback to hardcoded template
    return await sendSupportReplyEmailFallback(data)
  }
}

/**
 * Fallback support confirmation email
 */
async function sendSupportConfirmationEmailFallback(data: {
  customerName: string
  email: string
  ticketId: string
  subject: string
  priority: string
  message: string
  submittedDate: string
}) {
  const priorityColors = {
    LOW: '#10b981',
    NORMAL: '#3b82f6',
    HIGH: '#f59e0b',
    URGENT: '#ef4444'
  }

  const priorityColor = priorityColors[data.priority as keyof typeof priorityColors] || '#3b82f6'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Support Request Received - Forex Bot Zone</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fbbf24; margin: 0; font-size: 28px;">Support Request Received</h1>
        <p style="color: #e5e7eb; margin: 10px 0 0 0; font-size: 16px;">We've received your message and will respond soon</p>
      </div>

      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1f2937; margin-top: 0;">Hello ${data.customerName}!</h2>

        <p style="color: #4b5563; font-size: 16px; line-height: 1.6;">
          Thank you for contacting Forex Bot Zone support. We have received your message and our team will review it shortly.
        </p>

        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #fbbf24;">
          <h3 style="margin-top: 0; color: #1f2937;">Your Support Request Details</h3>
          <p><strong>Ticket ID:</strong> ${data.ticketId}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
          <p><strong>Priority:</strong> <span style="color: ${priorityColor}; font-weight: bold;">${data.priority}</span></p>
          <p><strong>Submitted:</strong> ${data.submittedDate}</p>
        </div>

        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">Your Message</h3>
          <p style="color: #4b5563; white-space: pre-wrap;">${data.message}</p>
        </div>

        <div style="background: #e0f2fe; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #0277bd;">What Happens Next?</h3>
          <ul style="color: #4b5563; margin: 0; padding-left: 20px;">
            <li>Our support team will review your request</li>
            <li>You'll receive a response within 24 hours</li>
            <li>We'll send updates to this email address</li>
            <li>Keep your ticket ID for reference</li>
          </ul>
        </div>

        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

        <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
          © 2024 Forex Bot Zone. All rights reserved.<br>
          This is an automated message. Please do not reply to this email.
        </p>
      </div>
    </body>
    </html>
  `

  return sendEmail({
    to: data.email,
    subject: 'Support Request Received - We\'ll Get Back to You Soon!',
    html,
  })
}

/**
 * Fallback support reply email
 */
async function sendSupportReplyEmailFallback(data: {
  customerName: string
  email: string
  ticketId: string
  subject: string
  status: string
  adminName: string
  responseContent: string
  responseDate: string
  supportUrl: string
}) {
  const statusColors = {
    OPEN: '#3b82f6',
    IN_PROGRESS: '#f59e0b',
    RESOLVED: '#10b981',
    CLOSED: '#6b7280'
  }

  const statusColor = statusColors[data.status as keyof typeof statusColors] || '#3b82f6'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Support Reply - Forex Bot Zone</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #1f2937 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fbbf24; margin: 0; font-size: 28px;">Support Team Response</h1>
        <p style="color: #e5e7eb; margin: 10px 0 0 0; font-size: 16px;">We've responded to your support request</p>
      </div>

      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <h2 style="color: #1f2937; margin-top: 0;">Hello ${data.customerName}!</h2>

        <p style="color: #4b5563; font-size: 16px; line-height: 1.6;">
          Our support team has responded to your request. Please find the details below.
        </p>

        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
          <h3 style="margin-top: 0; color: #1f2937;">Support Request Details</h3>
          <p><strong>Ticket ID:</strong> ${data.ticketId}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
          <p><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${data.status}</span></p>
          <p><strong>Responded by:</strong> ${data.adminName}</p>
          <p><strong>Response Date:</strong> ${data.responseDate}</p>
        </div>

        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">Our Response</h3>
          <div style="background: #f0fdf4; padding: 15px; border-radius: 6px; border-left: 3px solid #10b981;">
            <p style="color: #4b5563; white-space: pre-wrap; margin: 0;">${data.responseContent}</p>
          </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.supportUrl}"
             style="background: #fbbf24; color: #1f2937; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            View Full Conversation
          </a>
        </div>

        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #92400e;">Need Further Assistance?</h3>
          <p style="color: #92400e; margin: 0;">
            If you need additional help, please reply to this email or contact our support team directly.
          </p>
        </div>

        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

        <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
          © 2024 Forex Bot Zone. All rights reserved.<br>
          You can reply to this email to continue the conversation.
        </p>
      </div>
    </body>
    </html>
  `

  return sendEmail({
    to: data.email,
    subject: `Response to Your Support Request - ${data.subject}`,
    html,
  })
}

/**
 * Send support message notification to admin
 */
export async function sendSupportNotificationEmail(data: SupportMessageData) {
  const priorityColors = {
    LOW: '#10b981',
    NORMAL: '#3b82f6',
    HIGH: '#f59e0b',
    URGENT: '#ef4444'
  }

  const priorityColor = priorityColors[data.priority as keyof typeof priorityColors] || '#3b82f6'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Support Message</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: #1f2937; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: #fbbf24; margin: 0;">New Support Message</h1>
      </div>

      <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid ${priorityColor};">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h2 style="color: #1f2937; margin: 0;">Message Details</h2>
            <span style="background: ${priorityColor}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
              ${data.priority} PRIORITY
            </span>
          </div>

          <p><strong>From:</strong> ${data.name} ${data.isGuest ? '(Guest)' : '(Registered User)'}</p>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
          <p><strong>Message ID:</strong> ${data.id}</p>
          <p><strong>Submitted:</strong> ${data.createdAt.toLocaleString()}</p>
        </div>

        <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">Message</h3>
          <p style="white-space: pre-wrap; background: #f8fafc; padding: 15px; border-radius: 6px; border-left: 3px solid #e5e7eb;">${data.message}</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
          <a href="${process.env.SITE_URL}/admin/support"
             style="background: #fbbf24; color: #1f2937; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            View in Admin Panel
          </a>
        </div>
      </div>
    </body>
    </html>
  `

  return sendEmail({
    to: '<EMAIL>',
    subject: `[${data.priority}] New Support Message: ${data.subject}`,
    html,
    replyTo: data.email,
  })
}
