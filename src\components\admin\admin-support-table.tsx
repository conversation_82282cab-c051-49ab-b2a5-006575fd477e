'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import {
  MagnifyingGlassIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  UserIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'
import { formatDistanceToNow } from 'date-fns'
import { SupportMessageModal } from './support-message-modal'

interface SupportMessage {
  id: string
  name: string
  email: string
  subject: string
  message: string
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED'
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  userId?: string
  isGuest: boolean
  adminNotes?: string
  respondedAt?: string
  respondedBy?: string
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    firstName?: string
    lastName?: string
    email: string
  }
  responses: Array<{
    id: string
    content: string
    isAdmin: boolean
    createdAt: string
    admin?: {
      id: string
      firstName?: string
      lastName?: string
    }
  }>
}

interface PaginationData {
  page: number
  limit: number
  total: number
  pages: number
}

export function AdminSupportTable() {
  const [messages, setMessages] = useState<SupportMessage[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    search: ''
  })
  const [selectedMessage, setSelectedMessage] = useState<SupportMessage | null>(null)
  const [showModal, setShowModal] = useState(false)

  useEffect(() => {
    fetchMessages()
  }, [pagination.page, filters])

  const fetchMessages = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.status !== 'all' && { status: filters.status }),
        ...(filters.priority !== 'all' && { priority: filters.priority }),
        ...(filters.search && { search: filters.search })
      })

      const response = await fetch(`/api/support/messages?${params}`)
      const data = await response.json()

      if (data.success) {
        setMessages(data.data.messages)
        setPagination(data.data.pagination)
      } else {
        toast.error('Failed to load support messages')
      }
    } catch (error) {
      console.error('Error fetching support messages:', error)
      toast.error('Failed to load support messages')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchMessages()
  }

  const handleViewMessage = (message: SupportMessage) => {
    setSelectedMessage(message)
    setShowModal(true)
  }

  const handleMessageUpdate = () => {
    fetchMessages()
    setShowModal(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return <ExclamationTriangleIcon className="w-4 h-4 text-red-400" />
      case 'IN_PROGRESS':
        return <ClockIcon className="w-4 h-4 text-yellow-400" />
      case 'RESOLVED':
        return <CheckCircleIcon className="w-4 h-4 text-green-400" />
      case 'CLOSED':
        return <XCircleIcon className="w-4 h-4 text-gray-400" />
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      case 'IN_PROGRESS':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'RESOLVED':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'CLOSED':
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-blue-500/10 text-blue-400 border-blue-500/20'
      case 'NORMAL':
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
      case 'HIGH':
        return 'bg-orange-500/10 text-orange-400 border-orange-500/20'
      case 'URGENT':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search messages..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full pl-10 pr-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
              />
            </div>
          </div>
          
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
          >
            <option value="all">All Status</option>
            <option value="OPEN">Open</option>
            <option value="IN_PROGRESS">In Progress</option>
            <option value="RESOLVED">Resolved</option>
            <option value="CLOSED">Closed</option>
          </select>

          <select
            value={filters.priority}
            onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
            className="px-4 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
          >
            <option value="all">All Priority</option>
            <option value="LOW">Low</option>
            <option value="NORMAL">Normal</option>
            <option value="HIGH">High</option>
            <option value="URGENT">Urgent</option>
          </select>

          <Button type="submit" className="bg-yellow-400 hover:bg-yellow-500 text-black">
            Search
          </Button>
        </form>
      </div>

      {/* Messages Table */}
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto"></div>
            <p className="mt-2 text-gray-400">Loading support messages...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="p-8 text-center">
            <ChatBubbleLeftRightIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No Support Messages</h3>
            <p className="text-gray-400">No support messages found matching your criteria.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Message
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    From
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {messages.map((message) => (
                  <tr key={message.id} className="hover:bg-white/5">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-white truncate max-w-xs">
                          {message.subject}
                        </div>
                        <div className="text-sm text-gray-400 truncate max-w-xs">
                          {message.message}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {message.isGuest ? (
                          <UserIcon className="w-4 h-4 text-gray-400 mr-2" />
                        ) : (
                          <UserGroupIcon className="w-4 h-4 text-blue-400 mr-2" />
                        )}
                        <div>
                          <div className="text-sm font-medium text-white">{message.name}</div>
                          <div className="text-sm text-gray-400">{message.email}</div>
                          <div className="text-xs text-gray-500">
                            {message.isGuest ? 'Guest' : 'Registered User'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(message.status)}`}>
                        {getStatusIcon(message.status)}
                        <span className="ml-1">{message.status.replace('_', ' ')}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(message.priority)}`}>
                        {message.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-400">
                      {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                    </td>
                    <td className="px-6 py-4">
                      <Button
                        onClick={() => handleViewMessage(message)}
                        size="sm"
                        className="bg-blue-500 hover:bg-blue-600 text-white"
                      >
                        <EyeIcon className="w-4 h-4 mr-1" />
                        View
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="px-6 py-4 border-t border-white/10 flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} messages
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                size="sm"
                variant="outline"
              >
                Previous
              </Button>
              <Button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.pages}
                size="sm"
                variant="outline"
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Support Message Modal */}
      {selectedMessage && (
        <SupportMessageModal
          message={selectedMessage}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          onUpdate={handleMessageUpdate}
        />
      )}
    </div>
  )
}
