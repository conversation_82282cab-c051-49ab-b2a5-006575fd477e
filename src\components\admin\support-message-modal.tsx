'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import {
  XMarkIcon,
  UserIcon,
  UserGroupIcon,
  ClockIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline'
import { formatDistanceToNow } from 'date-fns'

interface SupportMessage {
  id: string
  name: string
  email: string
  subject: string
  message: string
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED'
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  userId?: string
  isGuest: boolean
  adminNotes?: string
  respondedAt?: string
  respondedBy?: string
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    firstName?: string
    lastName?: string
    email: string
  }
  responses: Array<{
    id: string
    content: string
    isAdmin: boolean
    createdAt: string
    admin?: {
      id: string
      firstName?: string
      lastName?: string
    }
  }>
}

interface SupportMessageModalProps {
  message: SupportMessage
  isOpen: boolean
  onClose: () => void
  onUpdate: () => void
}

export function SupportMessageModal({ message, isOpen, onClose, onUpdate }: SupportMessageModalProps) {
  const [loading, setLoading] = useState(false)
  const [responseContent, setResponseContent] = useState('')
  const [status, setStatus] = useState(message.status)
  const [priority, setPriority] = useState(message.priority)
  const [adminNotes, setAdminNotes] = useState(message.adminNotes || '')

  useEffect(() => {
    setStatus(message.status)
    setPriority(message.priority)
    setAdminNotes(message.adminNotes || '')
  }, [message])

  if (!isOpen) return null

  const handleUpdateMessage = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/support/messages/${message.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          priority,
          adminNotes,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Message updated successfully')
        onUpdate()
      } else {
        toast.error(data.message || 'Failed to update message')
      }
    } catch (error) {
      console.error('Error updating message:', error)
      toast.error('Failed to update message')
    } finally {
      setLoading(false)
    }
  }

  const handleAddResponse = async () => {
    if (!responseContent.trim()) {
      toast.error('Please enter a response')
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/support/messages/${message.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: responseContent,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Response added successfully')
        setResponseContent('')
        onUpdate()
      } else {
        toast.error(data.message || 'Failed to add response')
      }
    } catch (error) {
      console.error('Error adding response:', error)
      toast.error('Failed to add response')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      case 'IN_PROGRESS':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'RESOLVED':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'CLOSED':
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-blue-500/10 text-blue-400 border-blue-500/20'
      case 'NORMAL':
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
      case 'HIGH':
        return 'bg-orange-500/10 text-orange-400 border-orange-500/20'
      case 'URGENT':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-black bg-opacity-75" onClick={onClose} />

        <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-gray-900 shadow-xl rounded-2xl border border-white/10">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <h3 className="text-xl font-bold text-white">Support Message</h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(message.status)}`}>
                {message.status.replace('_', ' ')}
              </span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(message.priority)}`}>
                {message.priority}
              </span>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Message Details */}
            <div className="lg:col-span-2 space-y-6">
              {/* Original Message */}
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center mb-4">
                  {message.isGuest ? (
                    <UserIcon className="w-5 h-5 text-gray-400 mr-2" />
                  ) : (
                    <UserGroupIcon className="w-5 h-5 text-blue-400 mr-2" />
                  )}
                  <div>
                    <h4 className="font-medium text-white">{message.name}</h4>
                    <p className="text-sm text-gray-400">{message.email}</p>
                    <p className="text-xs text-gray-500">
                      {message.isGuest ? 'Guest' : 'Registered User'} • {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>
                <h5 className="font-medium text-white mb-2">{message.subject}</h5>
                <p className="text-gray-300 whitespace-pre-wrap">{message.message}</p>
              </div>

              {/* Responses */}
              {message.responses.length > 0 && (
                <div className="space-y-4">
                  <h4 className="font-medium text-white">Responses</h4>
                  {message.responses.map((response) => (
                    <div
                      key={response.id}
                      className={`p-4 rounded-lg ${
                        response.isAdmin
                          ? 'bg-blue-500/10 border-l-4 border-blue-500'
                          : 'bg-white/5 border-l-4 border-gray-500'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-white">
                          {response.isAdmin
                            ? `${response.admin?.firstName || 'Admin'} ${response.admin?.lastName || ''}`
                            : message.name
                          }
                        </span>
                        <span className="text-xs text-gray-400">
                          {formatDistanceToNow(new Date(response.createdAt), { addSuffix: true })}
                        </span>
                      </div>
                      <p className="text-gray-300 whitespace-pre-wrap">{response.content}</p>
                    </div>
                  ))}
                </div>
              )}

              {/* Add Response */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="font-medium text-white mb-3">Add Response</h4>
                <textarea
                  value={responseContent}
                  onChange={(e) => setResponseContent(e.target.value)}
                  placeholder="Type your response here..."
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 resize-none"
                />
                <div className="flex justify-end mt-3">
                  <Button
                    onClick={handleAddResponse}
                    disabled={loading || !responseContent.trim()}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    <PaperAirplaneIcon className="w-4 h-4 mr-2" />
                    Send Response
                  </Button>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status & Priority */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="font-medium text-white mb-4">Message Status</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Status
                    </label>
                    <select
                      value={status}
                      onChange={(e) => setStatus(e.target.value as any)}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400"
                      style={{
                        colorScheme: 'dark'
                      }}
                    >
                      <option value="OPEN" className="bg-gray-800 text-white">Open</option>
                      <option value="IN_PROGRESS" className="bg-gray-800 text-white">In Progress</option>
                      <option value="RESOLVED" className="bg-gray-800 text-white">Resolved</option>
                      <option value="CLOSED" className="bg-gray-800 text-white">Closed</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Priority
                    </label>
                    <select
                      value={priority}
                      onChange={(e) => setPriority(e.target.value as any)}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400"
                      style={{
                        colorScheme: 'dark'
                      }}
                    >
                      <option value="LOW" className="bg-gray-800 text-white">Low</option>
                      <option value="NORMAL" className="bg-gray-800 text-white">Normal</option>
                      <option value="HIGH" className="bg-gray-800 text-white">High</option>
                      <option value="URGENT" className="bg-gray-800 text-white">Urgent</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Admin Notes */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="font-medium text-white mb-4">Admin Notes</h4>
                <textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Internal notes (not visible to customer)"
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 resize-none"
                />
              </div>

              {/* Actions */}
              <div className="space-y-3">
                <Button
                  onClick={handleUpdateMessage}
                  disabled={loading}
                  className="w-full bg-yellow-400 hover:bg-yellow-500 text-black"
                >
                  {loading ? 'Updating...' : 'Update Message'}
                </Button>
              </div>

              {/* Message Info */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="font-medium text-white mb-3">Message Info</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Created:</span>
                    <span className="text-white">
                      {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                    </span>
                  </div>
                  {message.respondedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Responded:</span>
                      <span className="text-white">
                        {formatDistanceToNow(new Date(message.respondedAt), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">ID:</span>
                    <span className="text-white font-mono text-xs">{message.id}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
