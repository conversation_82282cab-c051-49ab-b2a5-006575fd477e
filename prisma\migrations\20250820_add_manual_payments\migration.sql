-- Add manual payment support to orders table
ALTER TABLE "orders" ADD COLUMN "paymentProofUrl" TEXT;
ALTER TABLE "orders" ADD COLUMN "paymentProofFileName" TEXT;
ALTER TABLE "orders" ADD COLUMN "manualPaymentAmount" DECIMAL(10,2);
ALTER TABLE "orders" ADD COLUMN "manualPaymentCurrency" TEXT;
ALTER TABLE "orders" ADD COLUMN "manualPaymentAddress" TEXT;
ALTER TABLE "orders" ADD COLUMN "adminNotes" TEXT;

-- Create manual payments table for tracking manual payment submissions
CREATE TABLE "manual_payments" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USDT',
    "walletAddress" TEXT NOT NULL,
    "transactionHash" TEXT,
    "proofImageUrl" TEXT NOT NULL,
    "proofFileName" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reviewedAt" TIMESTAMP(3),
    "reviewedBy" TEXT,
    "adminNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "manual_payments_pkey" PRIMARY KEY ("id")
);

-- Create payment methods table for admin configuration
CREATE TABLE "payment_methods" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "currency" TEXT NOT NULL,
    "walletAddress" TEXT,
    "qrCodeUrl" TEXT,
    "discountPercentage" DECIMAL(5,2) DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "instructions" TEXT,
    "minimumAmount" DECIMAL(10,2),
    "maximumAmount" DECIMAL(10,2),
    "processingTime" TEXT,
    "sortOrder" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_methods_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints
ALTER TABLE "manual_payments" ADD CONSTRAINT "manual_payments_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "manual_payments" ADD CONSTRAINT "manual_payments_reviewedBy_fkey" FOREIGN KEY ("reviewedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create indexes
CREATE INDEX "manual_payments_orderId_idx" ON "manual_payments"("orderId");
CREATE INDEX "manual_payments_status_idx" ON "manual_payments"("status");
CREATE INDEX "payment_methods_type_idx" ON "payment_methods"("type");
CREATE INDEX "payment_methods_isActive_idx" ON "payment_methods"("isActive");

-- Insert default USDT payment method
INSERT INTO "payment_methods" ("id", "name", "type", "currency", "walletAddress", "discountPercentage", "isActive", "instructions", "processingTime", "updatedAt") VALUES
('usdt_trc20', 'USDT (TRC20)', 'MANUAL', 'USDT', 'TYourWalletAddressHere123456789', 20.00, true, 'Send USDT to the wallet address above and upload a screenshot of the transaction as proof. You will receive a 20% discount on your order total.', '1-24 hours', CURRENT_TIMESTAMP);
