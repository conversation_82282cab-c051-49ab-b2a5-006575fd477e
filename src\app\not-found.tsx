import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import {
  HomeIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'

export const metadata: Metadata = {
  title: '404 - Page Not Found | Forex Bot Zone',
  description: 'The page you are looking for could not be found. Return to our premium forex trading tools and expert advisors.',
}

const floatingElements = Array.from({ length: 8 }, (_, i) => ({
  id: i,
  x: Math.random() * 100,
  y: Math.random() * 100,
  size: Math.random() > 0.5 ? 'w-2 h-2' : 'w-1 h-1',
  delay: Math.random() * 2000,
}))

export default function NotFound() {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-trading-pattern opacity-5"></div>
      
      {/* Animated Grid */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:30px_30px] sm:bg-[size:50px_50px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
      
      {/* Floating Particles */}
      <div className="hidden sm:block">
        {floatingElements.map((element) => (
          <div
            key={element.id}
            className={`absolute ${element.size} bg-yellow-400/20 rounded-full animate-pulse`}
            style={{
              left: `${element.x}%`,
              top: `${element.y}%`,
              animationDelay: `${element.delay}ms`,
              animationDuration: '3s'
            }}
          />
        ))}
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,transparent_0%,rgba(0,0,0,0.6)_70%)]" />

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* 404 Number */}
          <div className="relative mb-8">
            <h1 className="text-8xl sm:text-9xl md:text-[12rem] lg:text-[14rem] font-bold text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text leading-none">
              404
            </h1>
            <div className="absolute inset-0 text-8xl sm:text-9xl md:text-[12rem] lg:text-[14rem] font-bold text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text leading-none animate-pulse opacity-50 blur-sm">
              404
            </div>
          </div>

          {/* Error Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse-glow">
              <ExclamationTriangleIcon className="w-8 h-8 sm:w-10 sm:h-10 text-black" />
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4">
              Page Not Found
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 mb-6 max-w-2xl mx-auto leading-relaxed">
              The trading opportunity you're looking for seems to have moved to a different market. 
              Let's get you back to our premium forex tools and expert advisors.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link href="/">
              <Button className="w-full sm:w-auto bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-semibold px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-glow">
                <HomeIcon className="w-5 h-5 mr-2" />
                Back to Home
              </Button>
            </Link>
            
            <Link href="/products">
              <Button variant="outline" className="w-full sm:w-auto border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105">
                <ChartBarIcon className="w-5 h-5 mr-2" />
                Browse Products
              </Button>
            </Link>
          </div>

          {/* Quick Navigation */}
          <div className="glass p-6 sm:p-8 rounded-xl border border-white/10 max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-white mb-6">Quick Navigation</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Link
                href="/products"
                className="flex items-center p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-yellow-500/50 transition-all duration-300 group"
              >
                <ChartBarIcon className="w-6 h-6 text-yellow-400 mr-3 group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="text-white font-medium">Expert Advisors</div>
                  <div className="text-gray-400 text-sm">Premium trading bots</div>
                </div>
              </Link>

              <Link
                href="/categories"
                className="flex items-center p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-yellow-500/50 transition-all duration-300 group"
              >
                <MagnifyingGlassIcon className="w-6 h-6 text-yellow-400 mr-3 group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="text-white font-medium">Browse Categories</div>
                  <div className="text-gray-400 text-sm">Find your trading style</div>
                </div>
              </Link>

              <Link
                href="/help"
                className="flex items-center p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-yellow-500/50 transition-all duration-300 group"
              >
                <ExclamationTriangleIcon className="w-6 h-6 text-yellow-400 mr-3 group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="text-white font-medium">Help Center</div>
                  <div className="text-gray-400 text-sm">Get support</div>
                </div>
              </Link>

              <Link
                href="/contact"
                className="flex items-center p-4 bg-gray-800/50 rounded-lg border border-gray-700/50 hover:border-yellow-500/50 transition-all duration-300 group"
              >
                <ArrowLeftIcon className="w-6 h-6 text-yellow-400 mr-3 group-hover:scale-110 transition-transform" />
                <div className="text-left">
                  <div className="text-white font-medium">Contact Us</div>
                  <div className="text-gray-400 text-sm">Need assistance?</div>
                </div>
              </Link>
            </div>
          </div>

          {/* Back Button */}
          <div className="mt-8">
            <button
              onClick={() => window.history.back()}
              className="text-gray-400 hover:text-yellow-400 transition-colors duration-300 flex items-center justify-center mx-auto"
            >
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Go back to previous page
            </button>
          </div>
        </div>
      </div>

      {/* Side Decorations */}
      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-32 bg-gradient-to-b from-transparent via-yellow-400 to-transparent opacity-50" />
      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-32 bg-gradient-to-b from-transparent via-yellow-400 to-transparent opacity-50" />
    </div>
  )
}
