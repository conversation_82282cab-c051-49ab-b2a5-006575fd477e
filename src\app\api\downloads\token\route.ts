import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth-utils'
import { createDownloadToken, getDownloadStatus } from '@/lib/download-manager'

// GET /api/downloads/token - Get download status for a product
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')

    if (!productId) {
      return NextResponse.json({
        success: false,
        message: 'Product ID is required'
      }, { status: 400 })
    }

    const status = await getDownloadStatus(user.id, productId)

    return NextResponse.json({
      success: true,
      data: status
    })

  } catch (error: any) {
    console.error('Error getting download status:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to get download status'
    }, { status: 500 })
  }
}

// POST /api/downloads/token - Create a download token for a purchased product
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const { productId } = await request.json()

    if (!productId) {
      return NextResponse.json({
        success: false,
        message: 'Product ID is required'
      }, { status: 400 })
    }

    const token = await createDownloadToken(
      user.id,
      productId
    )

    return NextResponse.json({
      success: true,
      message: 'Download token created successfully',
      data: {
        token: token.token,
        downloadCount: token.downloadCount
      }
    })

  } catch (error: any) {
    console.error('Error creating download token:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create download token'
    }, { status: 500 })
  }
}
