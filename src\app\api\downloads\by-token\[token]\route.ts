import { NextRequest, NextResponse } from 'next/server'
import { generateDownloadUrl } from '@/lib/download-manager'

interface RouteParams {
  params: {
    token: string
  }
}

// GET /api/downloads/by-token/[token] - Generate secure download URL
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { token } = params
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'Download token is required'
      }, { status: 400 })
    }

    // Get client information for logging
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const forwardedFor = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ipAddress = forwardedFor?.split(',')[0] || realIp || 'unknown'

    const result = await generateDownloadUrl(token, userAgent, ipAddress)

    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.error
      }, { status: 400 })
    }

    // Instead of returning the URL for the client to open, redirect directly to the file
    // This prevents the XML error and provides a better download experience
    return NextResponse.redirect(result.downloadUrl!)

  } catch (error: any) {
    console.error('Error generating download URL:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to generate download URL'
    }, { status: 500 })
  }
}
