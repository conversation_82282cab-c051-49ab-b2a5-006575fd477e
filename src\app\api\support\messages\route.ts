import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { z } from 'zod'
import { SupportMessageStatus, SupportMessagePriority } from '@prisma/client'

const createSupportMessageSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject is too long'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(5000, 'Message is too long'),
  priority: z.nativeEnum(SupportMessagePriority).optional().default(SupportMessagePriority.NORMAL),
})

// POST /api/support/messages - Create a new support message
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const body = await request.json()

    // Validate the request body
    const validatedData = createSupportMessageSchema.parse(body)

    // Create the support message
    const supportMessage = await prisma.supportMessage.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        subject: validatedData.subject,
        message: validatedData.message,
        priority: validatedData.priority,
        userId: session?.user?.id || null,
        isGuest: !session?.user?.id,
        status: SupportMessageStatus.OPEN,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    })

    // Send confirmation email to user
    try {
      const { sendSupportConfirmationEmail } = await import('@/lib/email')
      await sendSupportConfirmationEmail({
        customerName: validatedData.name,
        email: validatedData.email,
        ticketId: supportMessage.id,
        subject: validatedData.subject,
        priority: validatedData.priority,
        message: validatedData.message,
        submittedDate: supportMessage.createdAt.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      })
    } catch (emailError) {
      console.error('Failed to send support confirmation email:', emailError)
      // Don't fail the request if email fails
    }

    // Send notification email to admin (optional)
    try {
      const { sendSupportNotificationEmail } = await import('@/lib/email')
      await sendSupportNotificationEmail({
        id: supportMessage.id,
        name: validatedData.name,
        email: validatedData.email,
        subject: validatedData.subject,
        message: validatedData.message,
        isGuest: supportMessage.isGuest,
        priority: validatedData.priority,
        createdAt: supportMessage.createdAt,
      })
    } catch (emailError) {
      console.error('Failed to send support notification email:', emailError)
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Your support message has been submitted successfully. We will get back to you soon!',
      data: {
        id: supportMessage.id,
        status: supportMessage.status,
        createdAt: supportMessage.createdAt,
      }
    })

  } catch (error: any) {
    console.error('Error creating support message:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to submit support message'
    }, { status: 500 })
  }
}

// GET /api/support/messages - Get support messages (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (status && status !== 'all') {
      where.status = status
    }
    
    if (priority && priority !== 'all') {
      where.priority = priority
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { subject: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } },
      ]
    }

    // Get messages with pagination
    const [messages, total] = await Promise.all([
      prisma.supportMessage.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          },
          responses: {
            include: {
              admin: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                }
              }
            },
            orderBy: {
              createdAt: 'asc'
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.supportMessage.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        messages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error: any) {
    console.error('Error fetching support messages:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch support messages'
    }, { status: 500 })
  }
}
