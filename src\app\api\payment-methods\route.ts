import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/payment-methods - Get active payment methods
export async function GET() {
  try {
    const paymentMethods = await prisma.paymentMethod.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' }
    })

    // Convert Decimal to number for JSON serialization
    const serializedMethods = paymentMethods.map(method => ({
      ...method,
      discountPercentage: method.discountPercentage ? Number(method.discountPercentage) : 0,
      minimumAmount: method.minimumAmount ? Number(method.minimumAmount) : null,
      maximumAmount: method.maximumAmount ? Number(method.maximumAmount) : null
    }))

    return NextResponse.json({
      success: true,
      data: serializedMethods
    })

  } catch (error) {
    console.error('Error fetching payment methods:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch payment methods'
    }, { status: 500 })
  }
}
