'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RichTextEditor } from '@/components/ui/rich-text-editor'
import { ImageUpload } from '@/components/admin/image-upload'
import { DownloadableFileUpload } from '@/components/admin/downloadable-file-upload'
import {
  ArrowLeftIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import { ProductStatus } from '@prisma/client'

interface Category {
  id: string
  name: string
}

export default function NewProductPage() {
  const router = useRouter()
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    shortDescription: '',
    price: 0,
    originalPrice: 0,
    isOnSale: false,
    salePrice: 0,
    categoryId: '',
    status: ProductStatus.DRAFT,
    featured: false,
    tags: [] as string[],
    metaTitle: '',
    metaDescription: '',
    images: [] as string[]
  })
  const [tagsInput, setTagsInput] = useState('')

  const [downloadableFile, setDownloadableFile] = useState<{
    fileName: string
    fileUrl: string
    fileSize: number
    fileType: string
  } | null>(null)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data.data || [])
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
        toast.error('Failed to load categories')
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              type === 'number' ? Number(value) :
              value
    }))
  }

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagsInput(e.target.value)
  }

  const processTagsInput = () => {
    const tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag)
    setFormData(prev => ({ ...prev, tags }))
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value
    setFormData(prev => ({
      ...prev,
      name,
      slug: generateSlug(name)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    // Process tags input before submission
    processTagsInput()

    try {
      // Prepare the product data including downloadable file info
      const productData = {
        ...formData,
        fileKey: downloadableFile?.fileUrl || null,
        fileName: downloadableFile?.fileName || null,
        fileSize: downloadableFile?.fileSize || null
      }

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success('Product created successfully!')
        router.push('/admin/products')
      } else {
        toast.error(data.message || 'Failed to create product')
      }
    } catch (error) {
      console.error('Error creating product:', error)
      toast.error('Failed to create product')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/products">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-white">Add New Product</h1>
            <p className="text-gray-400">Create a new product for your store</p>
          </div>
        </div>
        
        <Button 
          type="submit" 
          form="product-form"
          disabled={saving}
          className="bg-yellow-500 hover:bg-yellow-600 text-black"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
              Creating...
            </>
          ) : (
            <>
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Product
            </>
          )}
        </Button>
      </div>

      {/* Form */}
      <div className="bg-gray-800 rounded-lg p-6">
        <form id="product-form" onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="name" className="text-white">Product Name *</Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleNameChange}
                required
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                placeholder="Enter product name"
              />
            </div>
            
            <div>
              <Label htmlFor="slug" className="text-white">URL Slug *</Label>
              <Input
                id="slug"
                name="slug"
                type="text"
                value={formData.slug}
                onChange={handleInputChange}
                required
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                placeholder="product-url-slug"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="shortDescription" className="text-white">Short Description</Label>
            <Textarea
              id="shortDescription"
              name="shortDescription"
              value={formData.shortDescription}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 bg-gray-700 border-gray-600 text-white"
              placeholder="Brief description for product cards"
            />
          </div>

          <div>
            <RichTextEditor
              label="Full Description"
              value={formData.description}
              onChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
              placeholder="Detailed product description with rich formatting and HTML support"
              disabled={loading}
            />
          </div>

          {/* Product Images */}
          <ImageUpload
            images={formData.images}
            onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
            maxImages={5}
            disabled={loading}
          />

          {/* Downloadable File */}
          <DownloadableFileUpload
            file={downloadableFile}
            onFileChange={setDownloadableFile}
            disabled={loading}
          />

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <Label htmlFor="price" className="text-white">Price ($) *</Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={handleInputChange}
                required
                className="mt-1 bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="originalPrice" className="text-white">Original Price ($)</Label>
              <Input
                id="originalPrice"
                name="originalPrice"
                type="number"
                step="0.01"
                min="0"
                value={formData.originalPrice}
                onChange={handleInputChange}
                className="mt-1 bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="salePrice" className="text-white">Sale Price ($)</Label>
              <Input
                id="salePrice"
                name="salePrice"
                type="number"
                step="0.01"
                min="0"
                value={formData.salePrice}
                onChange={handleInputChange}
                className="mt-1 bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>

          {/* Category and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="categoryId" className="text-white">Category *</Label>
              <select
                id="categoryId"
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                required
                className="mt-1 w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <Label htmlFor="status" className="text-white">Status</Label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="mt-1 w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                <option value={ProductStatus.DRAFT}>Draft</option>
                <option value={ProductStatus.PUBLISHED}>Published</option>
                <option value={ProductStatus.ARCHIVED}>Archived</option>
              </select>
            </div>
          </div>

          {/* Checkboxes */}
          <div className="flex items-center space-x-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="isOnSale"
                checked={formData.isOnSale}
                onChange={handleInputChange}
                className="rounded border-gray-600 text-yellow-400 focus:ring-yellow-400"
              />
              <span className="ml-2 text-white">On Sale</span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                className="rounded border-gray-600 text-yellow-400 focus:ring-yellow-400"
              />
              <span className="ml-2 text-white">Featured Product</span>
            </label>
          </div>

          {/* Download Settings */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <h3 className="text-lg font-medium text-blue-400 mb-2">Download Access</h3>
            <p className="text-sm text-gray-300">
              All products now provide lifetime download access with no limits or expiry dates.
              Customers can download their purchased products unlimited times.
            </p>
          </div>

          {/* Tags */}
          <div>
            <Label htmlFor="tags" className="text-white">Tags</Label>
            <Input
              id="tags"
              name="tags"
              type="text"
              value={tagsInput}
              onChange={handleTagsChange}
              onBlur={processTagsInput}
              className="mt-1 bg-gray-700 border-gray-600 text-white"
              placeholder="tag1, tag2, tag3"
            />
            <p className="text-sm text-gray-400 mt-1">Separate tags with commas</p>
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-md border border-yellow-500/30"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* SEO */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-white">SEO Settings</h3>
            
            <div>
              <Label htmlFor="metaTitle" className="text-white">Meta Title</Label>
              <Input
                id="metaTitle"
                name="metaTitle"
                type="text"
                value={formData.metaTitle}
                onChange={handleInputChange}
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                placeholder="SEO title for search engines"
              />
            </div>
            
            <div>
              <Label htmlFor="metaDescription" className="text-white">Meta Description</Label>
              <Textarea
                id="metaDescription"
                name="metaDescription"
                value={formData.metaDescription}
                onChange={handleInputChange}
                rows={3}
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                placeholder="SEO description for search engines"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}
