import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSecureDownloadUrl } from '@/lib/cloudflare-r2'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    // Find the download record
    const download = await prisma.download.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      },
      include: {
        product: {
          include: {
            downloadFiles: true
          }
        }
      }
    })

    if (!download) {
      return NextResponse.json({
        success: false,
        message: 'Download not found'
      }, { status: 404 })
    }

    // Download is always valid for lifetime access

    // Get the first download file (you might want to modify this to handle multiple files)
    const downloadFile = download.product.downloadFiles[0]
    if (!downloadFile) {
      return NextResponse.json({
        success: false,
        message: 'No download files available'
      }, { status: 404 })
    }

    try {
      // Generate secure download URL from Cloudflare R2
      const fileUrl = await generateSecureDownloadUrl(downloadFile.filePath, downloadFile.fileName, 3600)
      
      // Update download count
      await prisma.download.update({
        where: { id: download.id },
        data: {
          downloadCount: download.downloadCount + 1,
          lastDownloadAt: new Date()
        }
      })

      // Fetch the file and return it
      const fileResponse = await fetch(fileUrl)
      if (!fileResponse.ok) {
        throw new Error('Failed to fetch file from storage')
      }

      const fileBuffer = await fileResponse.arrayBuffer()
      
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': downloadFile.fileType || 'application/octet-stream',
          'Content-Disposition': `attachment; filename="${downloadFile.fileName}"`,
          'Content-Length': fileBuffer.byteLength.toString(),
        },
      })

    } catch (storageError) {
      console.error('Error accessing file storage:', storageError)
      return NextResponse.json({
        success: false,
        message: 'File temporarily unavailable'
      }, { status: 503 })
    }

  } catch (error: any) {
    console.error('Error processing download:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Download failed'
    }, { status: 500 })
  }
}
