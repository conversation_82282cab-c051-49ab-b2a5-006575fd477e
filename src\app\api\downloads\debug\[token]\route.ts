import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: {
    token: string
  }
}

// GET /api/downloads/debug/[token] - Debug download token information
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { token } = params
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'Download token is required'
      }, { status: 400 })
    }

    // Find and validate the token
    const downloadToken = await prisma.downloadToken.findUnique({
      where: { token },
      include: {
        product: {
          include: {
            downloadFiles: true
          }
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    if (!downloadToken) {
      return NextResponse.json({
        success: false,
        message: 'Invalid download token'
      })
    }

    // Debug information
    const debugInfo = {
      token: {
        id: downloadToken.id,
        token: downloadToken.token,
        isActive: downloadToken.isActive,
        expiresAt: downloadToken.expiresAt,
        downloadCount: downloadToken.downloadCount,
        maxDownloads: downloadToken.maxDownloads,
        lastDownloadAt: downloadToken.lastDownloadAt
      },
      product: {
        id: downloadToken.product.id,
        name: downloadToken.product.name,
        fileKey: downloadToken.product.fileKey,
        fileName: downloadToken.product.fileName,
        fileSize: downloadToken.product.fileSize?.toString(),
        downloadFiles: downloadToken.product.downloadFiles.map(file => ({
          id: file.id,
          fileName: file.fileName,
          fileUrl: file.fileUrl,
          fileSize: file.fileSize.toString(),
          fileType: file.fileType
        }))
      },
      user: downloadToken.user
    }

    return NextResponse.json({
      success: true,
      data: debugInfo
    })

  } catch (error: any) {
    console.error('Error debugging download token:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to debug download token'
    }, { status: 500 })
  }
}
