'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  DocumentIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
import Image from 'next/image'
import toast from 'react-hot-toast'
import { formatCurrency } from '@/lib/utils'

interface ManualPayment {
  id: string
  orderId: string
  paymentMethod: string
  amount: number
  currency: string
  walletAddress: string
  transactionHash?: string
  proofImageUrl: string
  proofFileName: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PROCESSING'
  submittedAt: string
  reviewedAt?: string
  adminNotes?: string
  order: {
    orderNumber: string
    email: string
    firstName: string
    lastName: string
    totalAmount: number
    user?: {
      firstName: string
      lastName: string
      email: string
    }
    items: Array<{
      quantity: number
      price: number
      product: {
        name: string
        slug: string
      }
    }>
  }
  reviewer?: {
    firstName: string
    lastName: string
    email: string
  }
}

export default function AdminPaymentsPage() {
  const [payments, setPayments] = useState<ManualPayment[]>([])
  const [filteredPayments, setFilteredPayments] = useState<ManualPayment[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<string>('PENDING')
  const [selectedPayment, setSelectedPayment] = useState<ManualPayment | null>(null)
  const [reviewNotes, setReviewNotes] = useState('')
  const [isReviewing, setIsReviewing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchPayments()
  }, [selectedStatus])

  useEffect(() => {
    filterPayments()
  }, [payments, searchQuery])

  const fetchPayments = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/manual-payments?status=${selectedStatus}`)
      const data = await response.json()

      if (data.success) {
        setPayments(data.data)
      } else {
        toast.error('Failed to fetch payments')
      }
    } catch (error) {
      console.error('Error fetching payments:', error)
      toast.error('Failed to fetch payments')
    } finally {
      setLoading(false)
    }
  }

  const filterPayments = () => {
    if (!searchQuery.trim()) {
      setFilteredPayments(payments)
      return
    }

    const query = searchQuery.toLowerCase()
    const filtered = payments.filter(payment =>
      payment.order.orderNumber.toLowerCase().includes(query) ||
      payment.order.email.toLowerCase().includes(query) ||
      payment.order.firstName.toLowerCase().includes(query) ||
      payment.order.lastName.toLowerCase().includes(query) ||
      payment.paymentMethod.toLowerCase().includes(query) ||
      payment.currency.toLowerCase().includes(query) ||
      (payment.transactionHash && payment.transactionHash.toLowerCase().includes(query))
    )
    setFilteredPayments(filtered)
  }

  const handleReviewPayment = async (paymentId: string, status: 'APPROVED' | 'REJECTED') => {
    setIsReviewing(true)

    try {
      const response = await fetch(`/api/manual-payments/${paymentId}/review`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status,
          adminNotes: reviewNotes
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Payment ${status.toLowerCase()} successfully`)
        setSelectedPayment(null)
        setReviewNotes('')
        fetchPayments()
      } else {
        toast.error(data.message || 'Failed to review payment')
      }
    } catch (error) {
      console.error('Error reviewing payment:', error)
      toast.error('Failed to review payment')
    } finally {
      setIsReviewing(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="secondary" className="bg-yellow-900/20 text-yellow-400 border-yellow-700">Pending</Badge>
      case 'APPROVED':
        return <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-700">Approved</Badge>
      case 'REJECTED':
        return <Badge variant="secondary" className="bg-red-900/20 text-red-400 border-red-700">Rejected</Badge>
      case 'PROCESSING':
        return <Badge variant="secondary" className="bg-blue-900/20 text-blue-400 border-blue-700">Processing</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-white">Manual Payments</h1>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="md:hidden"
          >
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filters
          </Button>
        </div>

        {/* Search and Filters */}
        <div className={`space-y-4 ${showFilters ? 'block' : 'hidden md:block'}`}>
          {/* Search Bar */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search by order number, email, name, payment method, or transaction hash..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-400 font-medium mr-2 flex items-center">Status:</span>
            {['PENDING', 'APPROVED', 'REJECTED', 'PROCESSING'].map((status) => (
              <Button
                key={status}
                variant={selectedStatus === status ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStatus(status)}
                className={selectedStatus === status ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
              >
                {status}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
        </div>
      ) : filteredPayments.length === 0 ? (
        <div className="text-center py-12">
          <DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No payments found</h3>
          <p className="text-gray-400">
            {searchQuery ?
              `No payments match your search "${searchQuery}"` :
              `No manual payments with status "${selectedStatus}" found.`
            }
          </p>
          {searchQuery && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSearchQuery('')}
              className="mt-4"
            >
              Clear Search
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-400">
              Showing {filteredPayments.length} of {payments.length} payments
            </p>
          </div>

          <div className="grid gap-6">
            {filteredPayments.map((payment) => (
            <div key={payment.id} className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">
                    Order #{payment.order.orderNumber}
                  </h3>
                  <p className="text-gray-400 text-sm">
                    {payment.order.firstName} {payment.order.lastName} ({payment.order.email})
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  {getStatusBadge(payment.status)}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedPayment(payment)}
                  >
                    <EyeIcon className="w-4 h-4 mr-2" />
                    Review
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">Payment Method</p>
                  <p className="text-white font-medium">{payment.paymentMethod}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Amount</p>
                  <p className="text-white font-medium">
                    {formatCurrency(payment.amount)} {payment.currency}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Submitted</p>
                  <p className="text-white font-medium">
                    {new Date(payment.submittedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <CurrencyDollarIcon className="w-4 h-4" />
                    <span>Order Total: {formatCurrency(payment.order.totalAmount)}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <DocumentIcon className="w-4 h-4" />
                    <span>{payment.order.items.length} item(s)</span>
                  </div>
                </div>
                
                {payment.status === 'PENDING' && (
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReviewPayment(payment.id, 'REJECTED')}
                      className="border-red-600 text-red-400 hover:bg-red-900/20"
                    >
                      <XCircleIcon className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReviewPayment(payment.id, 'APPROVED')}
                      className="border-green-600 text-green-400 hover:bg-green-900/20"
                    >
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
          </div>
        </div>
      )}

      {/* Payment Review Modal */}
      {selectedPayment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 border border-gray-700 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-white">
                  Review Payment - Order #{selectedPayment.order.orderNumber}
                </h2>
                <button
                  onClick={() => setSelectedPayment(null)}
                  className="text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Payment Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Payment Information</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-400">Payment Method</p>
                      <p className="text-white">{selectedPayment.paymentMethod}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Amount</p>
                      <p className="text-white font-medium">
                        {formatCurrency(selectedPayment.amount)} {selectedPayment.currency}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Wallet Address</p>
                      <p className="text-white font-mono text-sm break-all">
                        {selectedPayment.walletAddress}
                      </p>
                    </div>
                    {selectedPayment.transactionHash && (
                      <div>
                        <p className="text-sm text-gray-400">Transaction Hash</p>
                        <p className="text-white font-mono text-sm break-all">
                          {selectedPayment.transactionHash}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Payment Proof</h3>
                  <div className="border border-gray-600 rounded-lg overflow-hidden">
                    <Image
                      src={selectedPayment.proofImageUrl}
                      alt="Payment proof"
                      width={400}
                      height={300}
                      className="w-full h-auto object-contain bg-gray-800"
                    />
                  </div>
                </div>
              </div>

              {/* Review Actions */}
              {selectedPayment.status === 'PENDING' && (
                <div className="border-t border-gray-700 pt-6">
                  <h3 className="text-lg font-medium text-white mb-4">Review Payment</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Admin Notes (Optional)
                      </label>
                      <textarea
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        placeholder="Add notes about this payment review..."
                        rows={3}
                        className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                      />
                    </div>
                    
                    <div className="flex space-x-4">
                      <Button
                        onClick={() => handleReviewPayment(selectedPayment.id, 'REJECTED')}
                        disabled={isReviewing}
                        variant="outline"
                        className="border-red-600 text-red-400 hover:bg-red-900/20"
                      >
                        {isReviewing ? (
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        ) : (
                          <XCircleIcon className="w-4 h-4 mr-2" />
                        )}
                        Reject Payment
                      </Button>
                      <Button
                        onClick={() => handleReviewPayment(selectedPayment.id, 'APPROVED')}
                        disabled={isReviewing}
                        variant="premium"
                      >
                        {isReviewing ? (
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        ) : (
                          <CheckCircleIcon className="w-4 h-4 mr-2" />
                        )}
                        Approve Payment
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
